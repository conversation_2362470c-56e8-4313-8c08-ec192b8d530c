package dummycontroller

import (
	"context"
	"fmt"
	"log"
	"sync"

	mNats "git.moderntv.eu/mcloud/nats"
	"git.moderntv.eu/mcloud/system/common"
	"github.com/nats-io/nats.go"
)

type taskState struct {
	State     string
	Done      bool
	Succeeded bool
}

type Dummy<PERSON><PERSON>roller struct {
	nc        *nats.Conn
	taskState map[int]taskState
	mu        sync.Mutex
}

func New(nc *nats.Conn) *DummyController {
	return &DummyController{
		nc:        nc,
		taskState: make(map[int]taskState),
	}
}

func (dc *DummyController) TaskState(dbID int) (ok bool, state string, done bool, succeeded bool) {
	dc.mu.Lock()
	defer dc.mu.Unlock()
	ts, ok := dc.taskState[dbID]
	if !ok {
		return
	}
	return true, ts.State, ts.Done, ts.Succeeded
}

func (dc *DummyController) ClearState() {
	dc.mu.Lock()
	defer dc.mu.Unlock()
	dc.taskState = make(map[int]taskState)
}

// Run listens for task state messages and confirm finite tasks them when they are done or failed.
// If the con
func (dc *DummyController) Run(ctx context.Context) (err error) {
	enc, err := nats.NewEncodedConn(dc.nc, nats.JSON_ENCODER)
	if err != nil {
		return
	}

	taskStateSub, err := enc.Subscribe("task_state.*", func(task *common.DbNodeTask) {
		id := task.OriginTask.DbId
		log.Printf("Task state: dbID=%d id=%s, state=%s, finite=%t", task.OriginTask.DbId, task.Id, task.State, task.OriginTask.Finite)
		dc.mu.Lock()
		current := dc.taskState[id]
		current.State = task.State
		if task.OriginTask.Finite && (task.State == "done" || task.State == "failed") {
			current.Done = true
			current.Succeeded = task.State == "done"
		}
		dc.taskState[id] = current
		dc.mu.Unlock()

		if task.State == "done" || task.State == "failed" {
			log.Printf("Confirm task: %s", task.Id)
			err2 := enc.Publish(fmt.Sprintf("task_confirmation.%s", mNats.EscapeRoutingKey(task.Id)), nil)
			if err2 != nil {
				log.Printf("ERROR: %v", err2)
			}
		}
	})
	if err != nil {
		return
	}
	defer func() {
		if err := taskStateSub.Unsubscribe(); err != nil {
			log.Printf("ERROR unsubscribing: %v", err)
		}
	}()

	log.Printf("DummyController listening for task state messages")
	<-ctx.Done()

	return
}
