package common

import (
	"encoding/json"
	"fmt"
	"time"

	. "git.moderntv.eu/go/common"
	"git.moderntv.eu/mcloud/log"
)

type Flags map[string]bool
type Resources map[string]float64

func NewResources() Resources {
	return make(map[string]float64)
}

func (self Resources) Json() []byte {
	json, err := json.Marshal(self)
	if err != nil {
		return []byte("{}")
	}

	return json
}

type MultiResources map[string][]float64

func NewMultiResources() MultiResources {
	return make(map[string][]float64)
}

func (self MultiResources) Json() []byte {
	json, err := json.Marshal(self)
	if err != nil {
		return []byte("{}")
	}

	return json
}

type SelectedResources map[string]int

func NewSelectedResources() SelectedResources {
	return make(map[string]int)
}

func (self SelectedResources) Json() []byte {
	json, err := json.<PERSON>(self)
	if err != nil {
		return []byte("{}")
	}

	return json
}

func Max(values []float64) float64 {
	var max float64
	for _, v := range values {
		if v > max {
			max = v
		}
	}
	return max
}

type DbTask struct {
	DbId                 int        `json:"db_id"`
	Id                   string     `json:"id"`
	Class                *string    `json:"class"`
	Resource             string     `json:"resource"`
	ResourceCode         string     `json:"resource_code"`
	Alternative          *string    `json:"alternative"`
	Order                int        `json:"order"`
	Level                int        `json:"level"`
	Revision             int        `json:"revision"`
	Action               string     `json:"action"`
	Finite               bool       `json:"finite"`
	Creation             *time.Time `json:"creation"` //TODO remove
	Requirements         *Flags     `json:"requirements"`
	RequiredResources    *Resources `json:"required_resources"`
	Parameters           *string    `json:"parameters"`
	Priority             *int       `json:"priority"`
	Comment              string     `json:"comment"`
	DependsOn            *string    `json:"depends_on"`
	After                *string    `json:"after"`
	RequiresFinishedDeps bool       `json:"requiresFinishedDeps"`
	PrivateData          *string    `json:"private_data"`
}

func (self *DbTask) String() string {
	alternative := ""
	if self.Alternative != nil {
		alternative = fmt.Sprintf("alternative: %s, ", *self.Alternative)
	}
	return fmt.Sprintf("%s:%d (%srevision: %d)", self.Id, self.DbId, alternative, self.Revision)
}

func (self *DbTask) Json() []byte {
	json, err := json.Marshal(self)
	if err != nil {
		return []byte("{}")
	}

	return json
}
func (self *DbTask) LogAction(logLevel log.LogLevel, event string, format string, args ...interface{}) {
	self.log(logLevel, event, format, args...)
}
func (self *DbTask) Log(logLevel log.LogLevel, format string, args ...interface{}) {
	self.log(logLevel, "", format, args...)
}
func (self *DbTask) log(logLevel log.LogLevel, event string, format string, args ...interface{}) {
	class := ""
	if self.Class != nil {
		class = *self.Class
	}
	alternative := ""
	if self.Alternative != nil {
		alternative = *self.Alternative
	}
	data := log.Data{
		"loglevel":         logLevel,
		"category":         log.CategoryMcloudTask,
		"resource":         self.Resource,
		"resource_code":    self.ResourceCode,
		"task_id":          self.Id,
		"task_revision":    self.Revision,
		"task_alternative": alternative,
		"task_class":       class,
	}
	if event != "" {
		data["event"] = event
	}
	log.DataPrintf(data, format, args...)
	Log(format, args...)
}

func (self *DbTask) RemovalReady() bool {
	return self.Action == "terminate" || self.Action == "wait"
}

type RunnableTask struct {
	DbTask

	SelectedResources SelectedResources `json:"selected_resources"`
	StartDelay        time.Duration     `json:"start_delay"`
}

func (self *RunnableTask) Json() []byte {
	json, err := json.Marshal(self)
	if err != nil {
		return []byte("{}")
	}

	return json
}

/*func (self *Cloud) GetDbNodeList(task *DbTask) []*DbNode {
	nodeDbTasks := make([]*DbNodeTask, 0, len(self.nodeDbTasks))
	for _, nodeDbTask := range self.nodeDbTasks {
		nodeDbTasks = append(nodeDbTasks, nodeDbTask)
	}

	return nodeDbTasks
}*/

type DbNodeTask struct {
	Task              int               `json:"task"`
	Node              string            `json:"node"`
	Revision          int               `json:"revision"`
	State             string            `json:"state"`
	Warnings          string            `json:"warnings"`
	LastOnlineTime    *time.Time        `json:"last_online_time"`
	LastRunningTime   *time.Time        `json:"last_running_time"`
	StartTime         *time.Time        `json:"start_time"`
	SelectedResources SelectedResources `json:"selected_resources"`
	ResourceUsage     MultiResources    `json:"resource_usage"`

	// additional fields not in the table
	PrivateData *string      `json:"private_data"`
	Status      string       `json:"status"`
	OriginTask  RunnableTask `json:"origin_task"`
	ApiData     string

	// add these to db structure?
	Id               string     `json:"id"`
	CreationTime     time.Time  `json:"creation_time"`
	RunningSinceTime *time.Time `json:"running_since_time"`
}

func (self *DbNodeTask) Json() []byte {
	json, err := json.Marshal(self)
	if err != nil {
		return []byte("{}")
	}

	return json
}

type ApiState struct {
	Id    int    `json:"id"`
	State string `json:"state"`
	Data  string `json:"data"`
}

func (self *ApiState) Json() []byte {
	json, err := json.Marshal(self)
	if err != nil {
		return []byte("{}")
	}

	return json
}

func (self *DbNodeTask) ApiState() *ApiState {
	return &ApiState{
		Id:    self.OriginTask.DbId,
		State: self.State,
		Data:  self.ApiData,
	}
}

type DbNode struct {
	Id           string     `json:"id"`
	Host         string     `json:"host"`
	LastOnline   *time.Time `json:"last_online"`
	State        string     `json:"state"`
	Capabilities *Flags     `json:"capabilities"`
	Enabled      bool       `json:"enabled"`

	AvailableResources MultiResources `json:"available_resources"`
	FreeResources      MultiResources `json:"free_resources"`
	TotalResources     MultiResources `json:"total_resources"`

	Group string `json:"group"` // TODO add to database
}

func (self *DbNode) Json() []byte {
	json, err := json.Marshal(self)
	if err != nil {
		return []byte("{}")
	}

	return json
}
func CreateNodeTaskId(dbTask *DbTask, hostname string) string {
	return "task_" + hostname + "_" + CreateNodeTaskSuffix(dbTask)
}
func CreateNodeTaskSuffix(dbTask *DbTask) string {
	revision := fmt.Sprintf("%d", dbTask.Revision)
	alternative := "_"
	if dbTask.Alternative != nil {
		alternative += *dbTask.Alternative + "_"
	}

	return dbTask.Id + alternative + revision
}

type CommandResult struct { //TODO json helper functions
	Task       RunnableTask
	Started    bool
	ReturnCode int
	Output     string
}

type DbController struct {
	Id              string     `json:"id"`
	Type            string     `json:"type"`
	Host            string     `json:"hostname"`
	State_          string     `json:"state"`
	Priority        int        `json:"priority"`
	HasDBConnection bool       `json:"has-db-connection"`
	LastOnline      time.Time  `json:"last-online-time"`
	LastMaster      *time.Time `json:"last-master-time"`
}

func (self *DbController) Json() []byte {
	json, err := json.Marshal(self)
	if err != nil {
		self.Log(log.LOG_ERROR, "ERROR creating json: %v", err)
		return []byte("{}")
	}

	return json
}

func NewDbController(json []byte) (*DbController, error) {
	var err error
	var controller DbController
	err = JsonParseBytes(json, &controller)
	if err != nil {
		return nil, err
	}

	return &controller, nil
}

func (self *DbController) Log(logLevel log.LogLevel, format string, args ...interface{}) {
	log.DataPrintf(log.Data{
		"loglevel": logLevel,
		"category": log.CategoryMcloudSystem,
		"id":       self.Id,
	}, format, args...)
	Log(format, args...)
}
