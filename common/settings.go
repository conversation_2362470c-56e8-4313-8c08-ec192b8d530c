package common

import (
	//"flag"
	"time"
)

/* global flags */
var (
//ShowVersion *bool = flag.Bool("version", false, "displays version information")
//Debug             = flag.Bool("debug", true, "debug mode") //TODO change default to false
)

/* common */
const DefaultConfigurationDirectory = "/etc/mcloud"

/* task */
const TaskRunningDeadline = 80 * time.Second

/* node */
const NodeStateInterval = 2 * time.Second

/* controller */
const ControllerStateInterval = 2 * time.Second

/* nodetask */
const NodeTaskStateInterval = 2 * time.Second
