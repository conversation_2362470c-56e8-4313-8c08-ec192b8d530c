package common

import (
	"encoding/json"
	"encoding/xml"
	"errors"
	"fmt"
	"os"
	"os/exec"
	"strconv"
	"strings"
)

const NvidiaSmiBin = "/opt/mcloud/bin/nvidia-smi"

type GPUDatabase map[string]GPUInfo

type GPUInfo struct { // TODO move out of nvidia.go
	Name        string `json:"name"`
	Nvenc       uint64 `json:"nvenc"`
	Nvdec       uint64 `json:"nvdec"`
	GPU         int    `json:"gpu"`
	PCIDeviceID string `json:"-"`
}

func GetNumGPUs() (int, error) {
	out, err := exec.Command(NvidiaSmiBin, "-L").Output()
	if err != nil {
		return 0, err
	}

	var count int
	lines := strings.Split(string(out), "\n")
	for _, line := range lines {
		if strings.HasPrefix(line, "GPU ") {
			count++
		}
	}
	return count, nil
}

type Percents uint64

func (self *Percents) UnmarshalXML(d *xml.Decoder, start xml.StartElement) error {
	var content string
	err := d.DecodeElement(&content, &start)
	if err != nil {
		return err
	}
	if content == "N/A" {
		*self = 0
		return nil
	}

	number, err := strconv.ParseUint(strings.TrimSuffix(content, " %"), 10, 64)
	if err != nil {
		return err
	}
	*self = Percents(number)
	return nil
}

type Megabytes uint64

func (self *Megabytes) UnmarshalXML(d *xml.Decoder, start xml.StartElement) error {
	var content string
	err := d.DecodeElement(&content, &start)
	if err != nil {
		return err
	}
	number, err := strconv.ParseUint(strings.TrimSuffix(content, " MiB"), 10, 64)
	if err != nil {
		return err
	}
	*self = Megabytes(number)
	return nil
}

type NvidiaSmiLog struct {
	// Timestamp     time.Time `xml:"timestamp"`
	DriverVersion string `xml:"driver_version"`
	GPUs          []struct {
		Id          string `xml:"id,attr"`
		ProductName string `xml:"product_name"`
		UUID        string `xml:"uuid"`
		Pci         struct {
			PciDeviceId string `xml:"pci_device_id"`
		} `xml:"pci"`
		FbMemoryUsage struct {
			Total Megabytes `xml:"total"`
			Used  Megabytes `xml:"used"`
			Free  Megabytes `xml:"free"`
		} `xml:"fb_memory_usage"`
		Utilization struct {
			GPU     Percents `xml:"gpu_util"`
			Encoder Percents `xml:"encoder_util"`
			Decoder Percents `xml:"decoder_util"`
		} `xml:"utilization"`
		Processes []struct {
			Pid        int       `xml:"pid"`
			UsedMemory Megabytes `xml:"used_memory"`
		} `xml:"processes>process_info"`
		AccountedProcesses []struct {
			Pid     int      `xml:"pid"`
			GpuUtil Percents `xml:"gpu_util"`
		} `xml:"accounted_processes>accounted_process_info"`
	} `xml:"gpu"`
}

func GetFreeGPUResources() (*NvidiaSmiLog, error) { // TODO rename function
	out, err := exec.Command(NvidiaSmiBin, "-q", "--xml-format").Output()
	if err != nil {
		return nil, err
	}

	var data NvidiaSmiLog
	err = xml.Unmarshal(out, &data)
	if err != nil {
		return nil, err
	}
	if data.DriverVersion == "" {
		return nil, errors.New("error getting data from nvidia-smi")
	}

	return &data, nil
}

func NewGPUDatabase() GPUDatabase {
	return make(GPUDatabase)
}

func (db GPUDatabase) GetGPUInfo(index int) (gpuinfo GPUInfo, uuid string, err error) {
	var data *NvidiaSmiLog
	data, err = GetFreeGPUResources()
	if err != nil {
		return
	}

	if index >= len(data.GPUs) {
		err = fmt.Errorf("no card at index %d", index)
		return
	}

	uuid = data.GPUs[index].UUID

	deviceId := data.GPUs[index].Pci.PciDeviceId
	var found bool
	gpuinfo, found = db[deviceId]
	if !found {
		gpuinfo = GPUInfo{
			Name:        "Unknown",
			PCIDeviceID: data.GPUs[index].Pci.PciDeviceId,
			Nvenc:       1,
			Nvdec:       1,
			GPU:         1,
		}
	}

	gpuinfo.PCIDeviceID = data.GPUs[index].Pci.PciDeviceId

	return
}

func (db GPUDatabase) Load(jsonFile string) error {
	var info map[string]GPUInfo
	file, err := os.Open(jsonFile)
	if err != nil {
		return err
	}
	defer file.Close()
	decoder := json.NewDecoder(file)
	err = decoder.Decode(&info)
	if err != nil {
		return err
	}

	for key, gpu := range info {
		if gpu.GPU < 1 {
			gpu.GPU = 1 // Ensure GPU is at least 1
		}
		if gpu.Nvenc < 1 {
			gpu.Nvenc = 1 // Ensure Nvenc is at least 1
		}
		if gpu.Nvdec < 1 {
			gpu.Nvdec = 1 // Ensure Nvdec is at least 1
		}
		db[key] = gpu
	}
	return nil
}
