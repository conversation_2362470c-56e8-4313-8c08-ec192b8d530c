package common

import (
	"encoding/json"
	"os"
	"path/filepath"
)

const (
	TaskOutputEphemeralDir = "e"
	TaskOutputDurableDir   = "d"
)

func ToJson(data interface{}) []byte {
	json, err := json.Marshal(data)
	if err != nil {
		return []byte("{}")
	}

	return json
}

type Specification map[string]json.RawMessage

func Exists(path string) (bool, error) {
	_, err := os.Stat(path)
	if err == nil {
		return true, nil
	}
	if os.IsNotExist(err) {
		return false, nil
	}
	return true, err
}

func RemoveDirContents(dir string) error {
	d, err := os.Open(dir)
	if err != nil {
		return err
	}
	defer d.Close()

	names, err := d.Readdirnames(-1)
	if err != nil {
		return err
	}
	for _, name := range names {
		err = os.RemoveAll(filepath.Join(dir, name))
		if err != nil {
			return err
		}
	}
	return nil
}
