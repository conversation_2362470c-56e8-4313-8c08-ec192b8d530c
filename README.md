# Overview
This repository contains controller, node and task.
Controller communicates with the database and executes tasks on nodes.
Nodes execute tasks and report hardware resources to the controller.
Tasks execute processes based on their specification and parameters.

All components use NATS to communicate.

# Components
* [Controller](controller/README.md)
* [Node](node/README.md)
* [Task](task/README.md)
