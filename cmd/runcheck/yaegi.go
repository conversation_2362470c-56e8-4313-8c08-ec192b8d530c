package main

import (
	"errors"
	"fmt"
	"os"
	"reflect"
	"strings"

	"git.moderntv.eu/mcloud/system/task/yaegi_dep"
	"github.com/traefik/yaegi/interp"
	"github.com/traefik/yaegi/stdlib"
)

const HookCheck = "Check"

var ErrBadFunctionSignature = errors.New("bad function signature")

func RunCheck(fn interface{}, binDir string, outputDir string, specificationJson []byte, args []string) (code int, info string, err error) {
	fni, ok := fn.(func(binDir string, outputDir string, specificationJson []byte, args []string) (code int, info string, err error))
	if !ok {
		err = ErrBadFunctionSignature
		return
	}

	code, info, err = fni(binDir, outputDir, specificationJson, args)
	return
}

func LoadScript(taskClass string, filepath string, hook string) (fn interface{}, err error) {
	if len(yaegi_dep.Symbols) == 0 {
		err = fmt.Errorf("yaegi symbols missing")
		return
	}

	intrp := interp.New(interp.Options{})
	intrp.Use(stdlib.Symbols)
	intrp.Use(yaegi_dep.Symbols)

	var src []byte
	src, err = os.ReadFile(filepath)
	if err != nil {
		return
	}

	_, err = intrp.Eval(string(src))
	if err != nil {
		err = fmt.Errorf("error evaluating %s: %w", filepath, err)
		return
	}

	var v reflect.Value
	packageName := strings.Replace(taskClass, "-", "_", -1)
	v, err = intrp.Eval(fmt.Sprintf("%s.%s", packageName, hook)) // package.Function
	if err != nil {
		return
	}

	fn = v.Interface()
	return
}
