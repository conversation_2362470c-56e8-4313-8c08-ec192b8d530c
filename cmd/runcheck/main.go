package main

import (
	"flag"
	"fmt"
	"os"
	"path/filepath"
)

func main() {
	os.Exit(mainRet())
}

func mainRet() int {
	var (
		script        string
		outputDir     string
		binDir        string
		specification string
		taskDir       string
	)
	flag.StringVar(&script, "check", "", "Path to check script")
	flag.StringVar(&outputDir, "outputs-dir", "", "Path to output directory")
	flag.StringVar(&taskDir, "task-dir", "", "Task directory")
	flag.StringVar(&specification, "specification", "{}", "JSON check specification")
	flag.Parse()

	var checkPath string
	if script != "" {
		checkPath = script
	} else {
		checkPath = filepath.Join(taskDir, "class", "check.go")
	}

	if outputDir == "" {
		outputDir = filepath.Join(taskDir, "outputs")
	}

	binDir = filepath.Join(taskDir, "class/bin")

	fn, err := LoadScript("tdata", checkPath, HookCheck)
	if err != nil {
		fmt.Fprintf(os.<PERSON>, "Cannot load check script: %v\n", err)
		return 2
	}

	code, info, err := RunCheck(fn, binDir, outputDir, []byte(specification), flag.Args())
	fmt.Printf("Code: %d\nInfo: %s\nErr: %v\n", code, info, err)

	return code
}
