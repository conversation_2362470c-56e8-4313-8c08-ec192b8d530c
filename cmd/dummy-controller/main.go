package main

import (
	"context"
	"log"
	"os"

	mNats "git.moderntv.eu/mcloud/nats"
	"git.moderntv.eu/mcloud/system/dummycontroller"
	"github.com/nats-io/nats.go"
)

func main() {
	os.Exit(mainRet())
}

func mainRet() int {
	hostname, err := os.Hostname()
	if err != nil {
		log.Printf("ERROR: %v", err)
		return 1
	}

	var nc *nats.Conn
	nc, err = mNats.Connect("controller_" + hostname)
	if err != nil {
		return 1
	}
	defer nc.Close()

	ctx := context.Background()

	controller := dummycontroller.New(nc)
	err = controller.Run(ctx)
	if err != nil {
		log.Printf("ERROR: %v", err)
		return 1
	}

	return 0
}
