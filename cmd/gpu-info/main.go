package main

import (
	"flag"
	"fmt"
	"os"

	"git.moderntv.eu/mcloud/system/common"
)

func main() {
	os.Exit(mainRet())
}

func mainRet() int {
	gpuDatabaseFile := flag.String("gpu-database", "/etc/mcloud/gpu.json", "path to the GPU database file")
	flag.Parse()

	gpuDatabase := common.NewGPUDatabase()

	if *gpuDatabaseFile != "" {
		err := gpuDatabase.Load(*gpuDatabaseFile)
		if err != nil {
			fmt.Fprintf(os.Stderr, "ERROR: Failed to load GPU database from %s: %v\n", *gpuDatabaseFile, err)
			return 2
		}
	}

	gpuCount, err := common.GetNumGPUs()
	if err != nil {
		fmt.Fprintf(os.Stderr, "ERROR: %s\n", err)
		return 1
	}

	for i := 0; i < gpuCount; i++ {
		info, uuid, err := gpuDatabase.GetGPUInfo(i)
		if err != nil {
			fmt.Fprintf(os.Stderr, "Failed to get info for GPU %d: %v\n", i, err)
			continue
		}
		fmt.Printf("%02d: name   = %s\n    PCI_ID = %s\n    uuid   = %s\n    nvenc  = %d\n    nvdec  = %d\n    gpu    = %d\n", i, info.Name, info.PCIDeviceID, uuid, info.Nvenc, info.Nvdec, info.GPU)
	}

	return 0
}
