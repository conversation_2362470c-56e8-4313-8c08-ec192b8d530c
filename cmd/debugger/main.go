package main

import (
	"flag"
	"fmt"
	"os"
	"strconv"
	"strings"
	"time"

	mNats "git.moderntv.eu/mcloud/nats"
	"github.com/nats-io/nats.go"
	"github.com/vharitonsky/iniflags"

	"git.moderntv.eu/mcloud/system/common"
)

func ago(t *time.Time) string {
	if t == nil {
		return ""
	}

	diff := time.Since(*t)
	if diff <= time.Second*3600 {
		units := (diff + time.Second/2) / time.Second
		return fmt.Sprintf("%d sec", units)
	} else if diff <= time.Minute*999 {
		units := (diff + time.Minute/2) / time.Minute
		return fmt.Sprintf("%d min", units)
	} else {
		units := (diff + time.Hour/2) / time.Hour
		return fmt.Sprintf("%d hours", units)
	}
}

func main() {
	var (
		queryTask      string
		queryResource  string
		queryClass     string
		timeoutSeconds int
	)
	flag.StringVar(&queryTask, "task", "", "Query tasks by task ID")
	flag.StringVar(&queryResource, "resource", "", "Query tasks by resource ID or resource code")
	flag.StringVar(&queryClass, "class", "", "Query tasks by class")
	flag.IntVar(&timeoutSeconds, "t", 7, "Wait time in seconds")
	iniflags.SetAllowUnknownFlags(true)
	iniflags.Parse()

	var err error

	var hostname string
	hostname, err = os.Hostname()
	if err != nil {
		fmt.Fprintf(os.Stderr, "ERROR: %s", err)
		os.Exit(1)
	}

	var nc *nats.Conn
	nc, err = mNats.Connect("debugger_" + hostname)
	if err != nil {
		fmt.Fprintf(os.Stderr, "ERROR: %s", err)
		os.Exit(1)
	}
	defer nc.Close()
	var enc *nats.EncodedConn
	enc, err = nats.NewEncodedConn(nc, nats.JSON_ENCODER)
	if err != nil {
		return
	}
	defer enc.Close()

	w := os.Stdout
	seen := make(map[string]bool)

	fmt.Fprintf(w, "%-20s %-40s %-26s %-15s %-10s %-10s  %-10s  %-10s\n", "Resource", "Task ID", "Alternative", "Node", "State", "Revision", "Online ago", "Start ago")

	_, err = enc.Subscribe("task_state.>", func(nodeTask *common.DbNodeTask) {
		task := nodeTask.OriginTask

		matches := true
		if queryResource != "" && task.Resource != queryResource && task.ResourceCode != queryResource {
			matches = false
		}

		if queryTask != "" && task.Id != queryTask {
			matches = false
		}
		if queryClass != "" && (task.Class == nil || *task.Class != queryClass) {
			matches = false
		}

		if matches {
			id := task.Id + "-" + *task.Alternative + "-" + nodeTask.Node + "-" + strconv.Itoa(nodeTask.Revision)

			if !seen[id] {
				node := nodeTask.Node
				node = strings.TrimPrefix(node, "node_")

				fmt.Fprintf(w, "%-20s %-40s %-26s %-15s %-10s %-10d  %-10s  %-10s\n", task.Resource, task.Id, *task.Alternative, node, nodeTask.State, nodeTask.Revision, ago(nodeTask.LastOnlineTime), ago(nodeTask.StartTime))
				seen[id] = true
			}
		}
	})
	if err != nil {
		fmt.Fprintf(os.Stderr, "Failed to subscribe to NATS: %s", err)
		os.Exit(1)
	}

	time.Sleep(time.Duration(timeoutSeconds) * time.Second)
}
