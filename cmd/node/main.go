package main

import (
	"flag"
	"os"
	"os/signal"
	"strconv"
	"strings"
	"syscall"
	"time"

	"github.com/nats-io/nats.go"
	"github.com/vharitonsky/iniflags"

	. "git.moderntv.eu/go/common"
	"git.moderntv.eu/mcloud/log"
	mNats "git.moderntv.eu/mcloud/nats"

	"git.moderntv.eu/mcloud/system/common"
	"git.moderntv.eu/mcloud/system/node"
)

var (
	taskOutputEphemeralDir = flag.String("task-output-ephemeral-dir", "/dev/shm/mcloud/outputs", "external directory for ephemeral task data output")
	taskOutputDurableDir   = flag.String("task-output-durable-dir", "", "external directory for durable task data output")
	taskOutputsDir         = flag.String("task-outputs-dir", "/var/mcloud/outputs", "directory for task data outputs")
	taskSpoolDir           = flag.String("task-spool-dir", "/var/mcloud/tasks", "directory for task working directories")
	taskClassDir           = flag.String("task-class-dir", "/opt/mcloud/classes", "directory with class definitions")
	taskSnapshotDir        = flag.String("task-snapshot-dir", "/var/mcloud/snapshots", "directory for task working directory snapshots")
	taskBinary             = flag.String("task-binary", "/opt/mcloud/task/task", "path to the task binary")
	taskExecUser           = flag.String("task-exec-user", "mcloud", "username under which the class processes will be executed in the task")
	taskExecGroup          = flag.String("task-exec-group", "mcloud", "group under which the class processes will be executed in the task")

	groupName            = flag.String("group", "unknown", "group name grouping servers in a single data center")
	initPath             = flag.String("init", "/usr/bin/dumb-init", "path to the init program used in class executions")
	gpuDatabase          = flag.String("gpu-database", "/etc/mcloud/gpu.json", "path to the GPU database file")
	gpuBlacklist         = flag.String("gpu-blacklist", "", "comma-separated list of UUIDs")
	detectBadGPU         = flag.Bool("detect-bad-gpu", true, "disable a GPU when the decoder is at 100%")
	resourceReservations = flag.String("reserve-resources", "CPU:5,mem:2,disk_d:10", "resource1:percentage1,resource2:percentage2,..")
)

func setupDirectories() (err error) {
	err = os.MkdirAll(*taskSpoolDir, 0o777)
	if err != nil {
		return
	}
	err = os.MkdirAll(*taskSnapshotDir, 0o777)
	if err != nil {
		return
	}
	err = os.MkdirAll(*taskOutputsDir, 0o777)
	if err != nil {
		return
	}

	create := func(outputPath string, linkDest string) (err error) {
		var ex bool
		ex, err = common.Exists(outputPath)
		if err != nil {
			return
		}
		if ex {
			// TODO check symlink
			return
		}

		if linkDest == "" {
			err = os.MkdirAll(outputPath, 0o777)
			if err != nil {
				return
			}
			return
		}

		err = os.MkdirAll(linkDest, 0o777)
		if err != nil {
			return
		}
		err = os.Symlink(linkDest, outputPath)
		if err != nil {
			return
		}

		return
	}
	err = create(*taskOutputsDir+"/"+common.TaskOutputEphemeralDir, *taskOutputEphemeralDir)
	if err != nil {
		return
	}
	err = create(*taskOutputsDir+"/"+common.TaskOutputDurableDir, *taskOutputDurableDir)
	if err != nil {
		return
	}

	return
}

func main() {
	iniflags.SetConfigFile(common.DefaultConfigurationDirectory + "/node.conf")
	iniflags.Parse()

	/*if *ShowVersion {
		fmt.Printf("%v\n", VERSION)
		return
	}*/

	hostname, err := os.Hostname()
	if err != nil {
		Log("ERROR: %s", err)
		os.Exit(1)
	}

	err = setupDirectories()
	if err != nil {
		Log("ERROR: %s", err)
		os.Exit(1)
	}

	rr := make(map[string]float64)
	for _, item := range strings.Split(*resourceReservations, ",") {
		pair := strings.SplitN(item, ":", 2)
		if len(pair) != 2 {
			Log("ERROR: invalid resource reservation: '%s'", item)
			os.Exit(2)
		}
		var perc float64
		perc, err = strconv.ParseFloat(pair[1], 64)
		if err != nil {
			Log("ERROR: invalid resource reservation: '%s' (%s)", item, err)
			os.Exit(2)
		}
		rr[pair[0]] = perc
	}

	conf := node.Conf{
		TaskOutputsDir:       *taskOutputsDir,
		TaskSpoolDir:         *taskSpoolDir,
		TaskClassDir:         *taskClassDir,
		TaskSnapshotDir:      *taskSnapshotDir,
		TaskBinary:           *taskBinary,
		TaskExecUser:         *taskExecUser,
		TaskExecGroup:        *taskExecGroup,
		Group:                *groupName,
		InitPath:             *initPath,
		GPUDatabase:          *gpuDatabase,
		GPUBlacklist:         strings.Split(*gpuBlacklist, ","),
		DetectBadGPU:         *detectBadGPU,
		ResourceReservations: rr,
	}

	var nc *nats.Conn
	nc, err = mNats.Connect("node_" + hostname)
	if err != nil {
		Log("ERROR: %s", err)
		os.Exit(1)
	}
	defer nc.Close()

	n, err := node.New(hostname, conf, nc)
	if err != nil {
		Log("ERROR: %s", err)
		os.Exit(1)
	}
	defer n.Close()

	mNats.AddHandlers(nc, func(format string, args ...interface{}) {
		n.Log(log.LOG_WARN, "NATS: "+format, args...)
	})

	c := make(chan os.Signal, 1)
	signal.Notify(c, syscall.SIGINT, syscall.SIGTERM)
	n.Log(log.LOG_WARN, "received signal: %v", <-c)

	go func() {
		time.Sleep(30 * time.Second) // TODO  constant
		n.Log(log.LOG_ERROR, "ERROR exit timed out")
		os.Exit(1)
	}()
}
