package main

import (
	"flag"
	"fmt"
	"os"
	"os/signal"
	"syscall"

	"github.com/nats-io/nats.go"
	"github.com/vbatts/go-cgroup"

	. "git.moderntv.eu/go/common"
	"git.moderntv.eu/mcloud/log"
	mNats "git.moderntv.eu/mcloud/nats"

	"git.moderntv.eu/mcloud/system/common"
	"git.moderntv.eu/mcloud/system/task"
)

var (
	taskJson     = flag.String("task", "", "JSON of corresponding Task")
	spoolDir     = flag.String("spool-dir", "/dev/shm/mcloud/tasks", "spool directory")
	snapshotDir  = flag.String("snapshot-dir", "/var/log/mcloud/snapshots", "directory for snapshots")
	maxSnapshots = flag.Int("max-snapshots", 1024, "global limit of stored snaphots")
	classDir     = flag.String("class-dir", "", "class directory")
	outputsDir   = flag.String("outputs-dir", "", "output directory")
	startDelay   = flag.Int("start-delay", 0, "milliseconds to wait before first process start")
	execUser     = flag.String("exec-user", "mcloud", "username under which the class processes will be executed")
	execGroup    = flag.String("exec-group", "mcloud", "group under which the class processes will be executed")
)

func main() {
	var err error

	flag.Parse()

	/*if *ShowVersion {
		fmt.Printf("%s\n", VERSION)
		return
	}*/

	var runnableTask common.RunnableTask
	err = JsonParseString(*taskJson, &runnableTask)
	if err != nil {
		fmt.Fprintf(os.Stderr, "ERROR parsing Task json: %s\n", err)
		os.Exit(103)
	}
	if runnableTask.Class == nil {
		fmt.Fprintf(os.Stderr, "ERROR: Task has no class\n")
		os.Exit(104)
	}

	var hostname string
	hostname, err = os.Hostname()
	if err != nil {
		return
	}

	err = cgroup.Init()
	if err != nil {
		fmt.Fprintf(os.Stderr, "ERROR: Failed to initialize cgroups\n")
		os.Exit(104)
	}

	// allow to set permissions manually
	syscall.Umask(0o00)

	conf := task.Conf{
		Hostname:     hostname,
		TaskJson:     *taskJson,
		SpoolDir:     *spoolDir,
		SnapshotDir:  *snapshotDir,
		MaxSnapshots: *maxSnapshots,
		ClassDir:     *classDir,
		OutputsDir:   *outputsDir,
		StartDelay:   *startDelay,
		ExecUser:     *execUser,
		ExecGroup:    *execGroup,
	}

	var nc *nats.Conn
	nc, err = mNats.Connect(common.CreateNodeTaskId(&runnableTask.DbTask, hostname))
	if err != nil {
		Log("ERROR: %s", err)
		os.Exit(105)
	}
	defer nc.Close()

	var rt *task.RunningTask
	rt, err = task.New(&runnableTask, conf, nc)
	if err != nil {
		fmt.Fprintf(os.Stderr, "ERROR: %s\n", err)
		os.Exit(106)
	}

	go func() {
		c := make(chan os.Signal, 16)
		signal.Notify(c, syscall.SIGINT, syscall.SIGTERM, syscall.SIGUSR1, syscall.SIGUSR2)
		var sig os.Signal
		for {
			sig = <-c
			switch sig {
			case syscall.SIGUSR1:
				rt.Log(log.LOG_WARN, "received signal from the node, stopping task")
			case syscall.SIGUSR2:
				rt.Log(log.LOG_WARN, "received signal from the iptv-task-ctl script, stopping task")
			default:
				rt.Log(log.LOG_WARN, "received unexpected signal '%s', stopping task", sig)
			}

			err = rt.Stop()
			if err != nil {
				rt.Log(log.LOG_ERROR, "error stopping task: %s", err)
			}
		}
	}()

	mNats.AddHandlers(nc, func(format string, args ...interface{}) {
		rt.Log(log.LOG_WARN, "NATS: "+format, args...)
	})

	err = rt.Run()
	if err != nil {
		Log("ERROR: %s", err)
		os.Exit(1)
	}
}
