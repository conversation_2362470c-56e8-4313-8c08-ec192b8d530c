package main

import (
	"flag"
	"fmt"
	"net/http"
	_ "net/http/pprof" //nolint:gosec
	"net/url"
	"os"
	"os/signal"
	"strconv"
	"syscall"
	"time"

	"github.com/nats-io/nats.go"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/vharitonsky/iniflags"

	"git.moderntv.eu/go/common"
	"git.moderntv.eu/mcloud/log"
	mNats "git.moderntv.eu/mcloud/nats"

	//"git.moderntv.eu/mcloud/system/common"
	"git.moderntv.eu/mcloud/system/controller"
	"git.moderntv.eu/mcloud/system/controller/cloud"
	"git.moderntv.eu/mcloud/system/controller/db"
	"git.moderntv.eu/mcloud/system/controller/runningcontroller"
)

var (
	dbHost     = flag.String("db-host", "", "database hostname")
	dbPort     = flag.String("db-port", "3306", "database port")
	dbUser     = flag.String("db-username", "", "database user")
	dbPassword = flag.String("db-password", "", "database password")
	dbDatabase = flag.String("db-database", "moderntvcloud", "database name")
)

var (
	showVersion = flag.Bool("version", false, "print version and terminate")
	apiUrl      = flag.String("api-url", "http://example.com/control/task-state", "TODO")
	priority    = flag.Int("priority", -1, "master selection priority: -1 for free cpu or a value from 0 to infinity")
	httpPort    = flag.Int("http-port", 0, "HTTP port for debugging interface, 0 is disabled")

	quorum       = flag.Int("quorum", 1, "the number of visible controllers for a master state to be allowed (ie. should be set to a majority for 3+ controllers)")
	workersTasks = flag.Int("workers-tasks", 5, "the number of task states to be processed in parallel")
)

var VERSION string

type DbLogger struct{}

func (logger DbLogger) Print(a ...interface{}) {
	str := "mysql:"
	for _, ax := range a {
		str = fmt.Sprintf("%s %v", str, ax)
	}
	log.DataPrintf(log.Data{
		"loglevel": log.LOG_ERROR,
		"category": log.CategoryMcloudSystem,
	}, str)
	common.Log(str)
}

func main() {
	go func() {
		server := &http.Server{
			Addr:              ":15424",
			ReadHeaderTimeout: 5 * time.Second,
			ReadTimeout:       10 * time.Second,
			WriteTimeout:      100 * time.Second,
		}
		err := server.ListenAndServe()

		common.Log("ERROR in debug listener: %v", err)
	}()

	hostname, err := os.Hostname()
	if err != nil {
		common.Log("ERROR: %v", err)
		os.Exit(1)
	}

	iniflags.Parse()

	log.SetLogger(log.NewSyslogLogger())
	log.SetData(log.Data{
		"program": "mcloud_controller",
		"server":  hostname,
	})

	if *showVersion {
		fmt.Printf("%v\n", VERSION)
		return
	}

	if *dbHost == "" {
		common.Log("ERROR: db-host parameter missing")
		flag.Usage()
		os.Exit(1)
	}

	db := db.NewDB(fmt.Sprintf("%v:%v@tcp(%v:%v)/%v?time_zone=%s&timeout=%v", *dbUser, *dbPassword, *dbHost, *dbPort, *dbDatabase, url.QueryEscape("'+00:00'"), "30s"), DbLogger{})

	metrics := controller.NewMetrics()
	metrics.MustRegister(prometheus.DefaultRegisterer)
	cloud := cloud.NewCloud(metrics)

	var nc *nats.Conn
	nc, err = mNats.Connect("controller_" + hostname)
	if err != nil {
		common.Log("ERROR: %s", err)
		os.Exit(1)
	}
	defer nc.Close()

	c, err := runningcontroller.New(hostname, VERSION, cloud, db, nc, metrics, *apiUrl, *priority, *quorum, *workersTasks)
	if err != nil {
		common.Log("ERROR: %s", err)
		os.Exit(1)
	}
	defer c.Close(controller.NewLocks())

	mNats.AddHandlers(nc, func(format string, args ...interface{}) {
		c.Log(log.LOG_WARN, "NATS: "+format, args...)
	})

	if *httpPort != 0 {
		go func() {
			server := &http.Server{
				Addr:              ":" + strconv.Itoa(*httpPort),
				Handler:           http.TimeoutHandler(http.HandlerFunc(c.ManagementHandler), 5*time.Second, "Timed out."),
				ReadHeaderTimeout: 5 * time.Second,
				ReadTimeout:       10 * time.Second,
			}
			err := server.ListenAndServe()
			common.Log("ERROR: %s", err)
		}()
	}

	sc := make(chan os.Signal, 2)
	signal.Notify(sc, syscall.SIGINT, syscall.SIGTERM)
	select {
	case s := <-sc:
		c.Log(log.LOG_INFO, "received signal: %v", s)
		os.Exit(2)
	case e := <-c.FatalErrors:
		c.Log(log.LOG_ERROR, "fatal ERROR: %s", e)
	case e := <-cloud.FatalErrors:
		c.Log(log.LOG_ERROR, "fatal ERROR: %s", e)
	}
	os.Exit(1)
}
