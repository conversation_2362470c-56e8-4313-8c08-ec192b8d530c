# Overview
Node is responsible for receiving and executing commands from controller.
It also gathers information about current resources and periodically sends them to the controller.

Task are executed with all the required flags and inside required namespaces.

# Resources
* `CPU`: free CPU in percents. Ie. 100 * core. Computed as average over the period of 15 seconds.
* `mem`: free main memory in MiB. Computed as minimum over the period of 5 seconds.
* `disk_e` free space in the ephemeral task output directory. Computed as the current value.
* `disk_d`: free space in the durable task output directory.Computed as the current value.
* `GPU`: percents of free GPU(s). Ie. 100 * chips. Computed as minimum over 15 seconds.
* `GPU_enc`: percents of free nvenc(s). Ie. 100 * chips. Computed as minimum over 15 seconds.
* `GPU_dec`: percents of free nvdec(s). Ie. 100 * chips. Computed as minimum over 15 seconds.
* `GPU_mem`: current free GPU memory in MiB. Computed as minimum over 15 seconds.

If a `disk_*` resource is based on a memory-backed filesystem, its free size is limited by the free main memory.
This free space is then also reserved in the main memory to avoid giving this (potentially filled) memory to tasks.

# GPU support
GPU data is retrieved using the nvidia-smi utility.

Supported GPUs and their nvenc/nvdec/GPU chip counts can be set in the `common.GPU` map.
Unknown cards have 1 of everything by default.

Broken GPUs are detected by the `GPU_dec` resource being permanently 100% utilized.
In such cases all the GPU-related resources of the broken GPU are set to 0.
