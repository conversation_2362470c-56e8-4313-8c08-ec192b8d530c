package node

import (
	"bufio"
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"os"
	"os/exec"
	"regexp"
	"runtime"
	"strconv"
	"strings"
	"sync"
	"syscall"
	"time"

	"github.com/nats-io/nats.go"

	. "git.moderntv.eu/go/common"
	"git.moderntv.eu/go/counter"
	"git.moderntv.eu/mcloud/log"
	mNats "git.moderntv.eu/mcloud/nats"
	mNatsFlag "git.moderntv.eu/mcloud/nats/flag"

	"git.moderntv.eu/mcloud/system/common"
)

var (
	meminfoLine = regexp.MustCompile(`^\w+:\s*(\d+)\s+kB$`)
	sizeLine    = regexp.MustCompile(`^\s*(\d+)\s*$`)
)

type Res struct {
	cpuTotalCounter       *counter.Counter
	cpuIdleCounter        *counter.Counter
	freeMemoryCounter     *counter.Counter
	freeDiskSpaceCounters map[string]*counter.Counter

	mu  sync.Mutex
	gpu struct {
		count      int
		current    *common.NvidiaSmiLog
		lastUpdate time.Time
		counters   []GPUCounters
	}
}
type GPUCounters struct {
	Enabled bool

	*common.GPUInfo
	freeGPU    *counter.Counter
	freeMemory *counter.Counter
	freeEnc    *counter.Counter
	freeDec    *counter.Counter
}

const (
	StateInit    = "initializing"
	StateRunning = "running"
	StateStopped = "stopped"
	StateError   = "error"
)

func IsTmpfs(path string) (bool, error) {
	out, err := exec.Command("df", "--output=fstype", path).Output()
	if err != nil {
		return false, err
	}
	return strings.HasSuffix(string(out), "\ntmpfs\n"), nil
}

func Capacity(path string) (float64, error) {
	out, err := exec.Command("df", "--output=size", path).Output()
	if err != nil {
		return 0, err
	}

	s := bufio.NewScanner(bytes.NewBuffer(out))
	for s.Scan() {
		line := s.Text()
		match := sizeLine.FindStringSubmatch(line)
		if len(match) != 2 {
			continue
		}
		value, err := strconv.ParseUint(match[1], 10, 64)
		if err != nil {
			return 0, err
		}
		return float64(value) / 1024, nil

	}
	err = s.Err()
	if err != nil {
		return 0, err
	}

	return 0, errors.New("failed to parse df output")
}

type Conf struct {
	TaskOutputsDir       string
	TaskSpoolDir         string
	TaskClassDir         string
	TaskSnapshotDir      string
	TaskBinary           string
	TaskExecUser         string
	TaskExecGroup        string
	Group                string
	InitPath             string
	GPUDatabase          string
	GPUBlacklist         []string
	DetectBadGPU         bool
	ResourceReservations map[string]float64
}

type RunningNode struct {
	Id   string
	Host string
	conf Conf
	enc  *nats.EncodedConn

	res Res

	stateSender *mNats.StateSender

	mu     sync.RWMutex
	state_ string
}

func gpuInfoPtr(info common.GPUInfo) *common.GPUInfo {
	return &info
}

func New(hostname string, conf Conf, nc *nats.Conn) (node *RunningNode, err error) {
	var enc *nats.EncodedConn
	enc, err = nats.NewEncodedConn(nc, nats.JSON_ENCODER)
	if err != nil {
		return
	}
	node = &RunningNode{
		Id:     "node_" + hostname,
		Host:   hostname,
		state_: StateInit,
		conf:   conf,
		enc:    enc,
	}

	log.SetLogger(log.NewSyslogLogger())
	log.SetData(log.Data{
		"program":  "mcloud_node",
		"category": log.CategoryMcloudSystem,
		"server":   hostname,
		"id":       node.Id,
	})

	gpuDatabase := common.NewGPUDatabase()
	if conf.GPUDatabase != "" {
		err = gpuDatabase.Load(conf.GPUDatabase)
		if err != nil {
			return
		}
	}

	node.res = Res{
		cpuTotalCounter:   counter.Create(counter.SourceFunc(GetCPUTotal), 1*time.Second, 15, false),
		cpuIdleCounter:    counter.Create(counter.SourceFunc(GetCPUIdle), 1*time.Second, 15, false),
		freeMemoryCounter: counter.Create(counter.SourceFunc(GetFreeMemory), 1*time.Second, 5, false),
	}

	var files []os.DirEntry
	files, err = os.ReadDir(conf.TaskOutputsDir)
	if err != nil {
		return
	}
	var subdirs []string
	for _, fi := range files {
		fileInfo, err := fi.Info()
		if err != nil {
			continue
		}
		if fileInfo.Mode()&(os.ModeSymlink|os.ModeDir) == 0 {
			continue
		}
		// TODO check that all subdirs are a different physical device
		subdirs = append(subdirs, fi.Name())
	}
	node.res.freeDiskSpaceCounters = make(map[string]*counter.Counter, len(subdirs))
	for _, subdir := range subdirs {
		node.res.freeDiskSpaceCounters[subdir] = counter.Create(counter.SourceFunc(node.GetOutputDirectoryFreeSpace(subdir)), 1*time.Second, 2, false)
	}
	node.res.gpu.count, err = common.GetNumGPUs()
	if err == nil && node.res.gpu.count > 0 {
		go func() {
			ticker := time.NewTicker(1 * time.Second)
			defer ticker.Stop()

			for {
				current, err := common.GetFreeGPUResources()
				if err != nil {
					node.Log(log.LOG_ERROR, "ERROR running nvidia-smi: %s", err)
				} else {
					node.res.mu.Lock()
					node.res.gpu.current = current
					node.res.gpu.lastUpdate = time.Now()
					node.res.mu.Unlock()
				}

				<-ticker.C
			}
		}()

		node.res.gpu.counters = make([]GPUCounters, node.res.gpu.count)
		var gpuinfo common.GPUInfo
		var uuid string
		for i := 0; i < node.res.gpu.count; i++ {
			gpuinfo, uuid, err = gpuDatabase.GetGPUInfo(i)
			if err != nil {
				node.Log(log.LOG_ERROR, "failed to get GPU info at index %d: %s", i, err)
				continue
			}
			node.Log(log.LOG_INFO, "detected GPU: %s", gpuinfo.Name)
			node.res.gpu.counters[i].GPUInfo = gpuInfoPtr(gpuinfo)

			enabled := true
			if uuid != "" {
				for _, g := range node.conf.GPUBlacklist {
					if g == uuid {
						enabled = false
						break
					}
				}
			}
			node.res.gpu.counters[i].Enabled = enabled
			if !node.res.gpu.counters[i].Enabled {
				node.Log(log.LOG_INFO, "GPU #%d ('%s' with UUID %s) is blacklisted", i, gpuinfo.Name, uuid)
				continue
			}

			node.res.gpu.counters[i].freeGPU = counter.Create(node.freeGPUFunc(i), 1*time.Second, 15, false)
			node.res.gpu.counters[i].freeEnc = counter.Create(node.freeGPUEncFunc(i), 1*time.Second, 15, false)
			node.res.gpu.counters[i].freeDec = counter.Create(node.freeGPUDecFunc(i), 1*time.Second, 15, false)
			node.res.gpu.counters[i].freeMemory = counter.Create(node.freeGPUMemoryFunc(i), 1*time.Second, 15, false)
		}
	}
	node.stateSender, err = mNats.NewStateSender(enc, node.GetState, "node_state."+mNats.EscapeRoutingKey(hostname), common.NodeStateInterval)
	if err != nil {
		return nil, err
	}
	err = node.listen()
	if err != nil {
		return nil, err
	}

	return node, nil
}

func (self *RunningNode) Close() {
	self.SetState(StateStopped)
	self.stateSender.Wait()
	self.enc.Close()
}

func (self *RunningNode) Log(logLevel log.LogLevel, format string, args ...interface{}) {
	log.DataPrintf(log.Data{
		"loglevel": logLevel,
	}, format, args...)
	Log(format, args...)
}

func (self *RunningNode) State(state string) bool {
	self.mu.RLock()
	defer self.mu.RUnlock()

	return self.state(state)
}

func (self *RunningNode) state(state string) bool {
	return self.state_ == state
}

func (self *RunningNode) setState(state string) {
	if self.state_ == state {
		return
	}
	self.Log(log.LOG_WARN, "state: %v -> %v", self.state_, state)
	self.state_ = state

	self.stateSender.StateBump()
}

func (self *RunningNode) SetState(state string) {
	self.mu.Lock()
	defer self.mu.Unlock()

	self.setState(state)
}

func (self *RunningNode) GetState() (interface{}, bool) {
	self.mu.RLock()
	defer self.mu.RUnlock()

	return self.getDbNode(), self.state(StateStopped)
}

var resourceNameCharBlacklist = regexp.MustCompile(`[^\w]`)

func (self *RunningNode) getDbNode() common.DbNode {
	var err error
	var tmp uint64
	var filled bool
	var free float64
	var total float64

	availableResources := common.NewMultiResources()
	freeResources := common.NewMultiResources()
	totalResources := common.NewMultiResources()

	if !self.state(StateStopped) {
		newState := StateRunning

		// 'CPU'
		free = 0
		numCPU := float64(runtime.NumCPU())
		if newState == StateRunning {
			filled, err = self.res.cpuTotalCounter.Filled()
			if err != nil {
				newState = StateError
			} else {
				if filled {
					totalDelta, _ := self.res.cpuTotalCounter.GetTotal(15)
					idleDelta, _ := self.res.cpuIdleCounter.GetTotal(15)
					if totalDelta > 0 {
						free = float64(idleDelta) / float64(totalDelta)
					}
					free *= numCPU * 100

				} else {
					newState = StateInit
				}
			}
		}
		total = numCPU * 100
		freeResources["CPU"] = []float64{free}
		availableResources["CPU"] = []float64{free}
		totalResources["CPU"] = []float64{total}

		// 'mem'
		var freeMemory float64
		free = 0
		if newState == StateRunning {
			filled, err = self.res.freeMemoryCounter.Filled()
			if err != nil {
				newState = StateError
			} else {
				if filled {
					tmp, _ = self.res.freeMemoryCounter.GetMin(5)
					free = float64(tmp)
					freeMemory = free
				} else {
					newState = StateInit
				}
			}
		}
		memTotal, _ := getMemoryData("MemTotal:")
		total = float64(memTotal)
		freeResources["mem"] = []float64{free}
		availableResources["mem"] = []float64{free}
		totalResources["mem"] = []float64{total}

		// 'disk_*'
		var tmpfsReserve float64
		if newState == StateRunning {
			for subdir, counter := range self.res.freeDiskSpaceCounters {
				free = 0

				var isTmpfs bool
				isTmpfs, err = IsTmpfs(self.conf.TaskOutputsDir + "/" + subdir)
				if err != nil {
					self.Log(log.LOG_ERROR, "IsTmpfs(): %s", err)
				}

				filled, err = counter.Filled()
				if err != nil {
					newState = StateError
				} else {
					if filled {
						tmp, _ = counter.GetLast()
						free = float64(tmp) / (1024 * 1024)
						if isTmpfs {
							free = math.Min(free, freeMemory) // TODO
							tmpfsReserve += free
						}
					} else {
						newState = StateInit
					}
				}

				id := fmt.Sprintf("disk_%s", resourceNameCharBlacklist.ReplaceAllLiteralString(subdir, "_")) // TODO conflicts
				tmp, _ = self.GetOutputDirectoryCapacity(subdir)
				total = float64(tmp) / (1024 * 1024)
				freeResources[id] = []float64{free}
				availableResources[id] = []float64{free}
				totalResources[id] = []float64{total}

				if id == "disk_e" { // TODO remove
					freeResources["disk"] = []float64{free}
					availableResources["disk"] = []float64{free}
					totalResources["disk"] = []float64{total}
				}
			}
		}
		// reserve main memory for tmpfs directories
		if tmpfsReserve > 0 {
			val := freeMemory - tmpfsReserve
			if val < 0 {
				val = 0
			}
			availableResources["mem"] = []float64{val}
		}

		var free float64
		var total float64
		if self.res.gpu.count > 0 {
			for cardIndex, card := range self.res.gpu.counters {
				//if !card.Enabled {
				//	continue
				//}

				// 'GPU'
				free = 0
				if card.freeGPU != nil && newState == StateRunning {
					filled, err = card.freeGPU.Filled()
					if err != nil {
						newState = StateError
					} else {
						if filled {
							tmp, _ = card.freeGPU.GetMin(15)
							free = float64(tmp)
						} else {
							newState = StateInit
						}
					}
				}
				total = 100
				if card.GPUInfo != nil {
					free *= float64(card.GPU)
					total *= float64(card.GPU)
				}
				freeResources["GPU"] = append(freeResources["GPU"], free)
				availableResources["GPU"] = append(availableResources["GPU"], free)
				totalResources["GPU"] = append(totalResources["GPU"], total)

				// 'GPU_mem'
				free = 0
				if card.freeMemory != nil && newState == StateRunning {
					filled, err = card.freeMemory.Filled()
					if err != nil {
						newState = StateError
					} else {
						if filled {
							tmp, _ = card.freeMemory.GetMin(15)
							free = float64(tmp)
						} else {
							newState = StateInit
						}
					}
				}
				tmp, _ = self.maxGPUMemory(cardIndex)
				total = float64(tmp)
				freeResources["GPU_mem"] = append(freeResources["GPU_mem"], free)
				availableResources["GPU_mem"] = append(availableResources["GPU_mem"], free)
				totalResources["GPU_mem"] = append(totalResources["GPU_mem"], total)

				var badGPU bool
				// 'GPU_enc'
				free = 0
				if card.freeEnc != nil && newState == StateRunning {
					filled, err = card.freeEnc.Filled()
					if err != nil {
						newState = StateError
					} else {
						if filled {
							tmp, _ = card.freeEnc.GetMin(15)
							free = float64(tmp)
						} else {
							newState = StateInit
						}
					}
				}
				total = 100
				if card.GPUInfo != nil {
					free *= float64(card.Nvenc)
					total *= float64(card.Nvenc)
				}
				freeResources["GPU_enc"] = append(freeResources["GPU_enc"], free)
				availableResources["GPU_enc"] = append(availableResources["GPU_enc"], free)
				totalResources["GPU_enc"] = append(totalResources["GPU_enc"], total)

				// 'GPU_dec'
				free = 0
				if card.freeDec != nil && newState == StateRunning {
					filled, err = card.freeDec.Filled()
					if err != nil {
						newState = StateError
					} else {
						if filled {
							tmp, _ = card.freeDec.GetMin(15)
							free = float64(tmp)

							tmp, _ = card.freeDec.GetMax(15)
							if tmp == 0 {
								badGPU = true
							}
						} else {
							newState = StateInit
						}
					}
				}
				total = 100
				if card.GPUInfo != nil {
					free *= float64(card.Nvdec)
					total *= float64(card.Nvdec)
				}
				freeResources["GPU_dec"] = append(freeResources["GPU_dec"], free)
				availableResources["GPU_dec"] = append(availableResources["GPU_dec"], free)
				totalResources["GPU_dec"] = append(totalResources["GPU_dec"], total)

				if self.conf.DetectBadGPU && badGPU {
					freeResources["GPU"][cardIndex] = 0
					freeResources["GPU_mem"][cardIndex] = 0
					freeResources["GPU_enc"][cardIndex] = 0
					freeResources["GPU_dec"][cardIndex] = 0

					availableResources["GPU"][cardIndex] = 0
					availableResources["GPU_mem"][cardIndex] = 0
					availableResources["GPU_enc"][cardIndex] = 0
					availableResources["GPU_dec"][cardIndex] = 0
				}
			}

			// add manual resource reservations
			for resource, values := range availableResources {
				var reserved float64
				reserved = self.conf.ResourceReservations[resource]
				if reserved <= 0 {
					continue
				}
				reserved /= 100.0

				for i := 0; i < len(values); i++ {
					avail := availableResources[resource][i]
					total := totalResources[resource][i]

					avail -= total * reserved
					if avail < 0 {
						avail = 0
					}
					availableResources[resource][i] = avail
				}
			}
		}
		self.setState(newState)
	}

	now := time.Now()
	node := common.DbNode{
		Id:                 self.Id,
		Host:               self.Host,
		AvailableResources: availableResources,
		FreeResources:      freeResources,
		TotalResources:     totalResources,
		LastOnline:         &now,
		State:              self.state_,
		Group:              self.conf.Group,
	}

	return node
}

func (self *RunningNode) listen() (err error) {
	self.Log(log.LOG_DEBUG, "waiting for commands")

	_, err = self.enc.Subscribe(fmt.Sprintf("node_command.%s", self.Host), func(task common.RunnableTask) {
		err := self.execute(&task)
		if err != nil {
			self.Log(log.LOG_ERROR, "execute ERROR: %s", err)
		}
	})
	return
}

func (self *RunningNode) execute(task *common.RunnableTask) error {
	id := common.CreateNodeTaskId(&task.DbTask, self.Host)
	if task.Action == "run" {
		task.Log(log.LOG_INFO, "starting task: %s", id)

		taskJson, err := json.Marshal(task)
		if err != nil {
			return err
		}

		params := []string{
			"--", self.conf.TaskBinary,
			"-spool-dir", self.conf.TaskSpoolDir,
			"-class-dir", self.conf.TaskClassDir,
			"-outputs-dir", self.conf.TaskOutputsDir,
			"-snapshot-dir", self.conf.TaskSnapshotDir,
			"-exec-user", self.conf.TaskExecUser,
			"-exec-group", self.conf.TaskExecGroup,
			"-task", string(taskJson),
		}
		if task.StartDelay > 0 {
			params = append(params, "-start-delay")
			params = append(params, strconv.FormatInt(task.StartDelay.Nanoseconds()/1e6, 10))
		}
		params = append(params, mNatsFlag.GetFlags()...)

		err = syscall.Mount("none", "/proc", "", syscall.MS_PRIVATE, "")
		if err != nil {
			return err
		}

		cmd := exec.Command(self.conf.InitPath, params...) // nolint:gosec
		cmd.SysProcAttr = &syscall.SysProcAttr{
			Cloneflags: syscall.CLONE_NEWPID | syscall.CLONE_NEWNS,
		}

		err = cmd.Start()
		if err != nil {
			return err
		}
		go func() {
			err := cmd.Wait()
			if err != nil {
				task.Log(log.LOG_ERROR, "task error: %s", err)
				exitErr, ok := err.(*exec.ExitError)
				if ok {
					Log("stderr: '%s'", exitErr.Stderr)
				}
			}
		}()
	} else if task.Action == "terminate" {
		task.Log(log.LOG_INFO, "stopping task: %v", id)

		pid_, err := os.ReadFile(fmt.Sprintf("%s/%s/task.pid", self.conf.TaskSpoolDir, common.CreateNodeTaskSuffix(&task.DbTask)))
		if err != nil {
			return err
		}
		pid, err := strconv.ParseInt(string(pid_), 10, 64)
		if err != nil {
			return err
		}
		err = syscall.Kill(int(pid), syscall.SIGUSR1)
		if err != nil {
			return err
		}
	} else {
		return fmt.Errorf("unknown node command: %v", task.Action)
	}
	return nil
}

func GetFreeMemory() (uint64, error) {
	return getMemoryData("MemAvailable:")
}

func getMemoryData(field string) (uint64, error) {
	file, err := os.Open("/proc/meminfo")
	if err != nil {
		return 0, err
	}
	defer file.Close()

	s := bufio.NewScanner(file)
	for s.Scan() {
		line := s.Text()
		if !strings.HasPrefix(line, field) {
			continue
		}
		match := meminfoLine.FindStringSubmatch(line)
		if len(match) != 2 {
			return 0, errors.New("failed to parse /proc/meminfo")
		}
		value, err := strconv.ParseUint(match[1], 10, 64)
		if err != nil {
			return 0, err
		}
		return value / 1024, nil

	}
	err = s.Err()
	if err != nil {
		return 0, err
	}
	return 0, errors.New("failed to parse /proc/meminfo")
}

func (self *RunningNode) GetOutputDirectoryFreeSpace(sub string) func() (uint64, error) {
	return func() (uint64, error) {
		var stat syscall.Statfs_t

		err := syscall.Statfs(self.conf.TaskOutputsDir+"/"+sub, &stat)
		if err != nil {
			return 0, err
		}

		return stat.Bavail * uint64(stat.Bsize), nil // nolint:gosec
	}
}

func (self *RunningNode) GetOutputDirectoryCapacity(sub string) (uint64, error) {
	var stat syscall.Statfs_t

	err := syscall.Statfs(self.conf.TaskOutputsDir+"/"+sub, &stat)
	if err != nil {
		return 0, err
	}

	return stat.Blocks * uint64(stat.Bsize), nil // nolint:gosec
}

func (self *RunningNode) freeGPUFunc(index int) counter.SourceFunc {
	return func() (uint64, error) {
		self.res.mu.Lock()
		defer self.res.mu.Unlock()

		if time.Since(self.res.gpu.lastUpdate) > GPUDataUpdateLimit {
			return 0, fmt.Errorf("GPU data not updated since %v", self.res.gpu.lastUpdate)
		}

		if self.res.gpu.current == nil || len(self.res.gpu.current.GPUs) <= index {
			return 0, fmt.Errorf("no such GPU at index %d", index)
		}
		return 100 - uint64(self.res.gpu.current.GPUs[index].Utilization.GPU), nil
	}
}

func (self *RunningNode) freeGPUEncFunc(index int) counter.SourceFunc {
	return func() (uint64, error) {
		self.res.mu.Lock()
		defer self.res.mu.Unlock()

		if time.Since(self.res.gpu.lastUpdate) > GPUDataUpdateLimit {
			return 0, fmt.Errorf("GPU data not updated since %v", self.res.gpu.lastUpdate)
		}

		if self.res.gpu.current == nil || len(self.res.gpu.current.GPUs) <= index {
			return 0, fmt.Errorf("no such GPU at index %d", index)
		}
		return 100 - uint64(self.res.gpu.current.GPUs[index].Utilization.Encoder), nil
	}
}

func (self *RunningNode) freeGPUDecFunc(index int) counter.SourceFunc {
	return func() (uint64, error) {
		self.res.mu.Lock()
		defer self.res.mu.Unlock()

		if time.Since(self.res.gpu.lastUpdate) > GPUDataUpdateLimit {
			return 0, fmt.Errorf("GPU data not updated since %v", self.res.gpu.lastUpdate)
		}

		if self.res.gpu.current == nil || len(self.res.gpu.current.GPUs) <= index {
			return 0, fmt.Errorf("no such GPU at index %d", index)
		}
		return 100 - uint64(self.res.gpu.current.GPUs[index].Utilization.Decoder), nil
	}
}

func (self *RunningNode) freeGPUMemoryFunc(index int) counter.SourceFunc {
	return func() (uint64, error) {
		self.res.mu.Lock()
		defer self.res.mu.Unlock()

		if time.Since(self.res.gpu.lastUpdate) > GPUDataUpdateLimit {
			return 0, fmt.Errorf("GPU data not updated since %v", self.res.gpu.lastUpdate)
		}

		if self.res.gpu.current == nil || len(self.res.gpu.current.GPUs) <= index {
			return 0, fmt.Errorf("no such GPU at index %d", index)
		}
		return uint64(self.res.gpu.current.GPUs[index].FbMemoryUsage.Free), nil
	}
}

func (self *RunningNode) maxGPUMemory(index int) (uint64, error) {
	self.res.mu.Lock()
	defer self.res.mu.Unlock()

	if self.res.gpu.current == nil || len(self.res.gpu.current.GPUs) <= index {
		return 0, fmt.Errorf("no such GPU at index %d", index)
	}
	return uint64(self.res.gpu.current.GPUs[index].FbMemoryUsage.Total), nil
}
