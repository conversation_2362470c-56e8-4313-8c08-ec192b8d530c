# Analýza a plán opravy resource management funkcionality

## Současný stav implementace (fix/optimize-resources branch)

### Architektura
Současná implementace používá modulární přístup s následujícími komponentami:

1. **Resources** - <PERSON><PERSON><PERSON><PERSON> k<PERSON> (controller/node/resources.go)
2. **ReservationRequirements** - spravuje okamžité rezervace pro nové/startující tasky
3. **PeakRunningRequirements** - spravuje potenciální rezervace pro běžící tasky
4. **TaskResourceInfo** - drž<PERSON> informace o rezervacích pro každý task

### Logika rozhodování o rezervacích
- **NeedsReservedResources()** rozhoduje, zda task potřebuje okamžité rezervace:
  - `true` pro: dummy, initializing, starting stavy
  - `true` pro: running tasky běžící < 15 sekund (RunningSinceTime)
  - `false` pro: running tasky b<PERSON>žící ≥ 15 sekund

### Sou<PERSON><PERSON><PERSON><PERSON> chování
1. **Reserved tasks** (< 15s): rezervují plnou required amount
2. **Non-reserved tasks** (≥ 15s): přispívají do "backup" rezervace (required - actual usage)
3. **Backup rezervace**: používá se maximum ze všech non-reserved tasků

## Původní implementace (master branch)

### Architektura
Monolitická implementace v NodeResources s:
- `reservations` - okamžité rezervace
- `reserves` - potenciální rezervace (required - usage)
- `maxReserve` - maximum ze všech reserves

### Klíčové rozdíly
1. **Jednodušší struktura** - vše v jedné struktuře
2. **Méně optimalizovaná** - při odstranění tasku se přepočítává celý maxReserve
3. **Stejná logika** - ReservedResources() funguje identicky

## Požadované změny

### 1. Resources budou rezervovány pouze pro naplánované a 15s běžící tasky
✅ **SPLNĚNO** - současná implementace už toto dělá správně

### 2. Pro tasky běžící > 15s se nerezervuje nic pro každý task
❌ **PROBLÉM** - současně se stále počítá backup rezervace

### 3. Najít task s největším rozdílem (required - actual) a přidat do rezervací
❌ **CHYBÍ** - současně se používá maximum, ne největší rozdíl jednoho tasku

### 4. Rozlišovat mezi nil a 0 pro resource usage
❌ **PROBLÉM** - není jasné rozlišení v logice

## Identifikované problémy

### 1. Backup rezervace logika
Současná implementace v `PeakRunningRequirements` počítá maximum ze všech tasků, ale podle požadavků by se měl najít **jeden task s největším rozdílem** a rezervovat jen ten.

### 2. Rozlišení nil vs 0
V kódu není jasné rozlišení mezi:
- Task nedeklaruje usage pro daný resource (nil/missing)
- Task deklaruje 0 usage

### 3. Test coverage
Testy neověřují novou požadovanou logiku - testují současné chování s maximum backup rezervací.

## Plán opravy

### Fáze 1: Analýza a příprava
- [x] Kompletní analýza současné implementace
- [x] Identifikace problémových míst
- [ ] Vytvoření testů pro novou logiku

### Fáze 2: Implementace změn
- [ ] Úprava PeakRunningRequirements pro "největší rozdíl" logiku
- [ ] Implementace rozlišení nil vs 0 pro resource usage
- [ ] Úprava Resources.AddTask() pro novou logiku

### Fáze 3: Testování a validace
- [ ] Spuštění existujících testů
- [ ] Vytvoření nových testů pro edge cases
- [ ] Benchmark testy pro výkon

## Detailní technické změny

### 1. PeakRunningRequirements změny
```go
// Místo maxReserve použít singleTaskReserve
type PeakRunningRequirements struct {
    singleTaskReserve map[string][]float64  // největší rozdíl jednoho tasku
    taskReserves      map[int]map[string][]float64
}
```

### 2. Logika výběru největšího rozdílu
- Při přidání/odebrání tasku přepočítat největší rozdíl
- Ignorovat tasky s nil usage pro daný resource
- Rozlišovat nil vs 0 usage

### 3. Úprava testů
Současné testy očekávají maximum backup rezervaci, ale nová logika bude používat jen největší rozdíl jednoho tasku.

## Rizika a opatření

### 1. Breaking changes
- Změna chování může ovlivnit scheduling
- Nutné důkladné testování

### 2. Performance
- Přepočítávání největšího rozdílu může být nákladné
- Optimalizace pro časté změny

### 3. Backward compatibility
- API zůstává stejné
- Změna jen interní logiky

## Analýza testů a potřebné opravy

### Problematické testy v resources_test.go

#### 1. Test "complex max reserve calculation" (řádky 719-801)
```go
// Současný test má 3 tasky:
// Task1: required=300, usage=50,  rozdíl=250
// Task2: required=400, usage=100, rozdíl=300 (největší)
// Task3: required=200, usage=150, rozdíl=50

// Současný test očekává maximum ze všech reserves (300)
// Available should be total - max_reserve = 1000 - 300 = 700
avail, _ := nr.GetAvailable("GPU")
is.Equal(avail, []float64{700.0})
```

**Problém**: Test očekává maximum (300) ze všech tasků, ale podle nových požadavků by se měl použít jen největší rozdíl jednoho tasku. V tomto případě je náhodou největší rozdíl (300) stejný jako maximum, ale logika je jiná.

**Oprava**: Test by měl být upraven tak, aby testoval správnou logiku - největší rozdíl jednoho tasku, ne maximum ze všech.

#### 2. Test "running node task" (řádky 94+)
Testy pro running tasky starší než 15 sekund neověřují správnou logiku podle nových požadavků.

#### 3. Scheduling integration test (scheduling_integration_test.go)
```go
// Řádek 149: očekává maximum reserve (1100)
// GPU: [1000, 1500, 1500-1100] = [1000, 1500, 400]
is.Equal(gpuAvail, []float64{1000.0, 1500.0, 400.0})
```

**Problém**: Test očekává maximum ze všech tasků místo největšího rozdílu jednoho tasku.

### Chybějící test cases

#### 1. Rozlišení nil vs 0 resource usage
```go
// Chybí test pro:
ResourceUsage: common.MultiResources{
    "GPU": {100.0}, // task deklaruje usage
    // "CPU" chybí - task nedeklaruje CPU usage (nil)
}
```

#### 2. Test pro "největší rozdíl" logiku
```go
// Potřeba test s více tasky kde největší rozdíl není maximum:
// Task1: required=500, usage=100, rozdíl=400
// Task2: required=300, usage=50,  rozdíl=250
// Task3: required=800, usage=600, rozdíl=200
// Největší rozdíl: Task1 (400), ne Task3 s nejvyšším required
```

#### 3. Edge case: všechny tasky mají nil usage pro resource
Test pro situaci kdy žádný task nedeklaruje usage pro daný resource.

## Konkrétní změny v testech

### 1. resources_test.go
- **Řádek 769**: změnit očekávanou hodnotu z 700.0 na hodnotu odpovídající největšímu rozdílu
- **Řádek 799**: změnit očekávanou hodnotu z 750.0 na novou logiku
- **Přidat nové testy** pro nil vs 0 rozlišení

### 2. scheduling_integration_test.go
- **Řádek 150**: změnit očekávanou hodnotu z 400.0 na hodnotu podle nové logiky
- **Řádek 174**: upravit očekávané hodnoty

### 3. peak_running_requirements_test.go
- **Kompletní přepis** - současné testy testují maximum logiku
- **Nové testy** pro single task největší rozdíl logiku

## Časový plán implementace

### Fáze 1: Příprava (1 den)
1. **Vytvoření nových testů** pro ověření požadovaného chování
2. **Úprava existujících testů** pro novou logiku
3. **Benchmark testy** pro porovnání výkonu

### Fáze 2: Implementace (2 dny)
1. **Implementovat rozlišení nil vs 0** v resource usage
2. **Přepsat PeakRunningRequirements** pro single task logic
3. **Upravit Resources.AddTask()** pro novou logiku

### Fáze 3: Testování a validace (1 den)
1. **Spuštění unit testů** pro ověření správnosti
2. **Benchmark testy** pro ověření výkonu
3. **Integrační testování** s celým systémem

## Příklad konkrétní implementace

### Příklad 1: Současný vs. nový přístup

**Současný přístup (maximum ze všech tasků):**
```go
// Tasky:
// Task1: required=300, usage=50,  rozdíl=250
// Task2: required=400, usage=100, rozdíl=300
// Task3: required=200, usage=150, rozdíl=50

// Současná logika:
// maxReserve = max(250, 300, 50) = 300
// available = total - maxReserve = 1000 - 300 = 700
```

**Nový přístup (největší rozdíl jednoho tasku):**
```go
// Tasky:
// Task1: required=300, usage=50,  rozdíl=250
// Task2: required=400, usage=100, rozdíl=300 (největší)
// Task3: required=200, usage=150, rozdíl=50

// Nová logika:
// Najít task s největším rozdílem: Task2 (rozdíl 300)
// maxReserve = rozdíl Task2 = 300
// available = total - maxReserve = 1000 - 300 = 700
```

V tomto případě je výsledek stejný, ale logika je jiná. Rozdíl bude vidět v následujícím příkladu:

### Příklad 2: Rozdíl mezi přístupy

**Tasky:**
```go
// Task1: required=500, usage=100, rozdíl=400 (největší rozdíl)
// Task2: required=800, usage=500, rozdíl=300
// Task3: required=900, usage=700, rozdíl=200
```

**Současná logika (maximum):**
```go
// maxReserve = max(400, 300, 200) = 400
// available = total - maxReserve = 1000 - 400 = 600
```

**Nová logika (největší rozdíl jednoho tasku):**
```go
// Najít task s největším rozdílem: Task1 (rozdíl 400)
// maxReserve = rozdíl Task1 = 400
// available = total - maxReserve = 1000 - 400 = 600
```

Opět stejný výsledek, ale z jiného důvodu. Rozdíl bude vidět, když přidáme další resource:

```go
// Task1: required=500, usage=100, rozdíl GPU=400, rozdíl CPU=200
// Task2: required=800, usage=500, rozdíl GPU=300, rozdíl CPU=400 (největší pro CPU)
```

**Současná logika (maximum):**
```go
// maxReserve GPU = max(400, 300) = 400
// maxReserve CPU = max(200, 400) = 400
```

**Nová logika (největší rozdíl jednoho tasku):**
```go
// Najít task s největším rozdílem pro GPU: Task1 (rozdíl 400)
// Najít task s největším rozdílem pro CPU: Task2 (rozdíl 400)
// maxReserve GPU = rozdíl Task1 = 400
// maxReserve CPU = rozdíl Task2 = 400
```

## Shrnutí

Současná implementace v branch `fix/optimize-resources` je dobrým základem, ale vyžaduje několik klíčových změn:

1. **Změna logiky rezervací** - místo maximum ze všech tasků použít největší rozdíl jednoho tasku
2. **Rozlišení nil vs 0** - správně rozlišovat mezi nedeklarovaným usage a nulovým usage
3. **Aktualizace testů** - upravit testy pro novou logiku

Tyto změny zajistí, že:
- Resources budou rezervovány jen pro tasky běžící < 15 sekund
- Pro tasky běžící > 15 sekund se bude rezervovat jen největší rozdíl jednoho tasku
- Tasky, které nedeklarují usage pro daný resource, budou ignorovány

Implementace by měla být zpětně kompatibilní na API úrovni, ale změní interní logiku alokace zdrojů, což může mít vliv na scheduling a využití zdrojů. Proto je důležité důkladné testování před nasazením do produkce.

## Implementační detaily

### Rozlišení nil vs 0 usage
```go
// V AddTask() logice:
usage := dbNodeTask.ResourceUsage[res]
if usage == nil {
    // Task nedeklaruje tento resource - ignorovat pro backup rezervaci
    continue
}
if len(usage) == l && usage[i] == 0 {
    // Task deklaruje 0 usage - započítat do backup rezervace
    reserveAmount = reqValue - 0 // = reqValue
}
```

### Single task reserve logika
```go
// Místo maximum najít největší rozdíl:
func (prr *PeakRunningRequirements) findLargestDifference(resourceType string) (int, []float64) {
    var largestDiffTaskId int
    var largestDiff []float64
    var maxDiffValue float64

    for taskId, taskReserves := range prr.taskReserves {
        if reserves := taskReserves[resourceType]; reserves != nil {
            for i, diff := range reserves {
                if diff > maxDiffValue {
                    maxDiffValue = diff
                    largestDiffTaskId = taskId
                    largestDiff = copySlice(reserves)
                    break // použij celý task s největším rozdílem
                }
            }
        }
    }
    return largestDiffTaskId, largestDiff
}
```

### Úprava PeakRunningRequirements struktury
```go
// Přidání informace o tasku s největším rozdílem
type PeakRunningRequirements struct {
    maxReserve         map[string][]float64            // největší rozdíl jednoho tasku
    maxReserveTaskId   map[string]int                  // ID tasku s největším rozdílem
    taskReserves       map[int]map[string][]float64    // všechny rezervace
}

// Inicializace
func NewPeakRunningRequirements() PeakRunningRequirements {
    return PeakRunningRequirements{
        maxReserve:       make(map[string][]float64),
        maxReserveTaskId: make(map[string]int),
        taskReserves:     make(map[int]map[string][]float64),
    }
}
```

### Úprava AddTaskReserve metody
```go
// Přidání rezervace pro task a aktualizace největšího rozdílu
func (prr *PeakRunningRequirements) AddTaskReserve(taskID int, resourceType string, reserves []float64) {
    if len(reserves) == 0 {
        return
    }

    // Inicializace task reserves
    if prr.taskReserves[taskID] == nil {
        prr.taskReserves[taskID] = make(map[string][]float64)
    }

    // Uložení rezervace tasku
    prr.taskReserves[taskID][resourceType] = copySlice(reserves)

    // Najít task s největším rozdílem
    taskId, largestDiff := prr.findLargestDifference(resourceType)

    // Aktualizovat maxReserve a maxReserveTaskId
    prr.maxReserve[resourceType] = largestDiff
    prr.maxReserveTaskId[resourceType] = taskId
}
```

### Úprava RemoveTaskReserve metody
```go
// Odstranění rezervace tasku a přepočítání největšího rozdílu
func (prr *PeakRunningRequirements) RemoveTaskReserve(taskID int, resourceType string) {
    if prr.taskReserves[taskID] == nil {
        return
    }

    reserves := prr.taskReserves[taskID][resourceType]
    if reserves == nil {
        return
    }

    // Kontrola, zda tento task měl největší rozdíl
    needsRecalc := false
    if prr.maxReserveTaskId[resourceType] == taskID {
        needsRecalc = true
    }

    // Odstranění rezervace tasku
    delete(prr.taskReserves[taskID], resourceType)
    if len(prr.taskReserves[taskID]) == 0 {
        delete(prr.taskReserves, taskID)
    }

    // Přepočítání největšího rozdílu, pokud je potřeba
    if needsRecalc {
        taskId, largestDiff := prr.findLargestDifference(resourceType)
        prr.maxReserve[resourceType] = largestDiff
        prr.maxReserveTaskId[resourceType] = taskId
    }
}
```
