package task

import (
	"fmt"
	"io/ioutil"
	"path/filepath"
	"reflect"
	"strings"

	"github.com/traefik/yaegi/interp"
	"github.com/traefik/yaegi/stdlib"

	"git.moderntv.eu/mcloud/log"

	"git.moderntv.eu/mcloud/system/task/yaegi_dep"
)

func (self *RunningTask) yaegiLoad(dir string) (err error) {
	defer func() {
		r := recover()
		if r != nil {
			err = fmt.Errorf("panic: %v", r)
		}
	}()

	if len(yaegi_dep.Symbols) == 0 {
		err = fmt.Errorf("yaegi symbols missing")
		return
	}

	var files []string
	files, err = filepath.Glob(fmt.Sprintf("%s/*.go", dir))
	if err != nil {
		return
	}
	for _, file := range files {
		self.Log(log.LOG_DEBUG, "(yaegi) loading %s", file)

		intrp := interp.New(interp.Options{})
		intrp.Use(stdlib.Symbols)
		intrp.Use(yaegi_dep.Symbols)

		var src []byte
		src, err = ioutil.ReadFile(file)
		if err != nil {
			return
		}

		_, err = intrp.Eval(string(src))
		if err != nil {
			err = fmt.Errorf("error evaluating %s: %w", file, err)
			return
		}

		hook := strings.TrimSuffix(filepath.Base(file), ".go")
		self.interpreters[hook] = intrp

		var v reflect.Value
		packageName := strings.Replace(*self.Task.Class, "-", "_", -1)
		v, err = intrp.Eval(fmt.Sprintf("%s.%s", packageName, strings.Title(hook))) // package.Function
		if err != nil {
			return
		}
		fn := v.Interface()
		self.fns[hook] = fn
	}

	return
}
func (self *RunningTask) yaegiFn(hook string) (fn interface{}, err error) {
	fn = self.fns[hook]
	if fn == nil {
		return
	}

	return
}
