package task

import (
	"bufio"
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"os/exec"
	"os/user"
	"path/filepath"
	"reflect"
	"sort"
	"strconv"
	"strings"
	"sync"
	"syscall"
	"time"

	linuxproc "github.com/c9s/goprocinfo/linux"
	"github.com/nats-io/nats.go"
	"github.com/otiai10/copy"
	"github.com/traefik/yaegi/interp"
	"github.com/vbatts/go-cgroup"
	"gopkg.in/natefinch/lumberjack.v2"

	gcommon "git.moderntv.eu/go/common"
	"git.moderntv.eu/go/counter"
	"git.moderntv.eu/mcloud/classes"
	"git.moderntv.eu/mcloud/log"
	mNats "git.moderntv.eu/mcloud/nats"
	"git.moderntv.eu/mcloud/system/common"
	"git.moderntv.eu/mcloud/system/pkg/flock"
)

const (
	ClassStart   = "start"
	ClassSuccess = "success"
	ClassFailure = "failure"
	ClassCheck   = "check"
	ClassData    = "data"
	ClassStatus  = "status"
)

const (
	StateInit     = "initializing"
	StateStarting = "starting"
	StateRunning  = "running"
	StateDone     = "done"
	StateFailed   = "failed"
	StateDeleted  = "deleted"
)

const (
	TimeoutExit = 10 * time.Second
	TimeoutStop = 2 * time.Second
)

const ControllerStateMaster = "master" // TODO use from a common package

type Res struct {
	cpuCounter           *counter.Counter
	usedDiskSpaceCounter *counter.Counter
	usedMemoryCounter    *counter.Counter

	mu  sync.Mutex // gpu.current
	gpu struct {
		count    int
		current  *common.NvidiaSmiLog
		counters []GPUCounters
	}
}
type GPUCounters struct {
	usedGPU    *counter.Counter
	usedMemory *counter.Counter
	// usedEnc    *counter.Counter
	// usedDec    *counter.Counter
}

type Conf struct {
	Hostname     string
	TaskJson     string
	SpoolDir     string
	SnapshotDir  string
	MaxSnapshots int
	ClassDir     string
	OutputsDir   string
	StartDelay   int
	ExecUser     string
	ExecGroup    string
}

type RunningTask struct {
	Id            string
	Task          common.RunnableTask
	Node          string
	Dir           string
	Conf          Conf
	Specification common.Specification

	execUid      uint32
	execGid      uint32
	enc          *nats.EncodedConn
	logPath      string
	confirmed    chan bool
	SkipTimeout  chan bool
	DirSuffix    string
	CreationTime time.Time
	Outputs      map[string]Output

	// yeagi
	interpreters map[string]*interp.Interpreter
	fns          map[string]interface{}

	// cgroups
	cgroup     cgroup.Cgroup
	cpuacct    *cgroup.Controller
	cgroupPath string

	*mNats.StateSender

	mu                         sync.RWMutex
	cond                       *sync.Cond
	state_                     string
	res                        Res
	startTime                  time.Time
	cmd                        *exec.Cmd
	sid                        int64
	warnings                   string
	stopped                    bool
	apiData                    string
	privateData                *string
	status                     string
	runningSinceTime           *time.Time
	flock                      *flock.Flock
	prepared                   bool
	flocked                    bool
	takenOutputs               bool
	takingOutputs              bool
	outputLocks                map[string]*flock.Flock
	lastOnlineMasterController time.Time
}

func New(task *common.RunnableTask, conf Conf, nc *nats.Conn) (rt *RunningTask, err error) {
	alternative := ""
	if task.Alternative != nil {
		alternative = *task.Alternative
	}
	class := ""
	if task.Class != nil {
		class = *task.Class
	}
	creationTime := time.Now()
	log.SetLogger(log.NewSyslogLogger())
	log.SetData(log.Data{
		"program":          "mcloud_task",
		"server":           conf.Hostname,
		"category":         log.CategoryMcloudTask,
		"resource":         task.Resource,
		"resource_code":    task.ResourceCode,
		"task_id":          task.Id,
		"task_revision":    task.Revision,
		"task_alternative": alternative,
		"task_class":       class,
		"task_creation":    creationTime,
	})

	suffix := common.CreateNodeTaskSuffix(&task.DbTask)
	id := common.CreateNodeTaskId(&task.DbTask, conf.Hostname)

	dir := conf.SpoolDir + "/" + suffix

	var specification common.Specification
	if task.Parameters == nil {
		err = errors.New("no Task parameters")
		return
	}
	err = gcommon.JsonParseString(*task.Parameters, &specification)
	if err != nil {
		err = fmt.Errorf("cannot parse json: %w", err)
	}

	// lookup uid/gid by their names
	var usr *user.User
	usr, err = user.Lookup(conf.ExecUser)
	if err != nil {
		return
	}
	var grp *user.Group
	grp, err = user.LookupGroup(conf.ExecGroup)
	if err != nil {
		return
	}
	// assume POSIX uid/gid format
	var uid uint64
	uid, err = strconv.ParseUint(usr.Uid, 10, 64)
	if err != nil {
		return
	}
	var gid uint64
	gid, err = strconv.ParseUint(grp.Gid, 10, 64)
	if err != nil {
		return
	}

	var enc *nats.EncodedConn
	enc, err = nats.NewEncodedConn(nc, nats.JSON_ENCODER)
	if err != nil {
		return
	}
	rt = &RunningTask{
		Task:          *task,
		Id:            id,
		Dir:           dir,
		Node:          "node_" + conf.Hostname, // TODO receive from the node
		Conf:          conf,
		Specification: specification,
		execUid:       uint32(uid),
		execGid:       uint32(gid),
		DirSuffix:     suffix,
		logPath:       filepath.Join(dir, "task.log"),
		SkipTimeout:   make(chan bool, 1),
		confirmed:     make(chan bool, 1),
		CreationTime:  creationTime,
		outputLocks:   make(map[string]*flock.Flock),
		enc:           enc,

		interpreters: make(map[string]*interp.Interpreter),
		fns:          make(map[string]interface{}),

		privateData: task.PrivateData,
		state_:      StateInit,
	}
	rt.cond = sync.NewCond(rt.mu.RLocker())

	rt.res = Res{
		cpuCounter:           counter.Create(counter.SourceFunc(rt.getCPUTicks), 1*time.Second, 15, false),
		usedDiskSpaceCounter: counter.Create(counter.SourceFunc(rt.GetOutputDirectoryUsedSpace), 15*time.Second, 1, false),
		usedMemoryCounter:    counter.Create(counter.SourceFunc(rt.GetUsedMemory), 5*time.Second, 3, false),
	}

	/* 	XXX: temporarily disable GPU resources.
	nvidia-smi does not work inside a PID namespace
	See https://github.com/NVIDIA/nvidia-docker/issues/179 */
	//rt.res.gpu.count, err = GetNumGPUs()
	if err == nil && rt.res.gpu.count > 0 && rt.Task.Class != nil && *rt.Task.Class == "ffmpeg" { // TODO don't hardcode class name
		go func() {
			ticker := time.NewTicker(5 * time.Second)
			defer ticker.Stop()

			for {
				current, err := common.GetFreeGPUResources()
				if err != nil {
					rt.Log(log.LOG_ERROR, "%s", err)
				} else {
					rt.res.mu.Lock()
					rt.res.gpu.current = current
					rt.res.mu.Unlock()
				}

				<-ticker.C
			}
		}()

		rt.res.gpu.counters = make([]GPUCounters, rt.res.gpu.count)
		for i := 0; i < rt.res.gpu.count; i++ {
			rt.res.gpu.counters[i].usedGPU = counter.Create(rt.usedGPUFunc(i), 5*time.Second, 3, false)
			// rt.res.gpu.counters[i].usedEnc = counter.Create(counter.SourceFunc(rt.usedGPUEncFunc(i)), 1*time.Second, 2, false)
			// rt.res.gpu.counters[i].usedDec = counter.Create(counter.SourceFunc(rt.usedGPUDecFunc(i)), 1*time.Second, 2, false)
			rt.res.gpu.counters[i].usedMemory = counter.Create(rt.usedGPUMemoryFunc(i), 5*time.Second, 3, false)
		}
	} else {
		rt.res.gpu.count = 0
	}
	rt.StateSender, err = mNats.NewStateSender(enc, rt.getState, "task_state."+mNats.EscapeRoutingKey(rt.Task.Id), common.NodeTaskStateInterval)
	if err != nil {
		return nil, err
	}

	err = rt.FLock()
	if err != nil {
		return
	}
	// only now we can use task.Log()

	/*go func() {
		var err error
		for i := 0; i < 16; i++ {
			port := rand.Int()%49000 + 1000
			rt.Log(log.LOG_INFO, "debug port: %d", port)
			err = http.ListenAndServe(fmt.Sprintf(":%d", port), nil)
		}

		Log("ERROR in debug listener: %s", err)
	}()*/

	rt.listen()

	rt.LogEvent(log.LOG_INFO, "start", "start")
	return
}

func (self *RunningTask) LogEvent(logLevel log.LogLevel, event string, format string, args ...interface{}) {
	self.log(logLevel, event, format, args...)
}

func (self *RunningTask) Log(logLevel log.LogLevel, format string, args ...interface{}) {
	self.log(logLevel, "", format, args...)
}

func (self *RunningTask) log(logLevel log.LogLevel, event string, format string, args ...interface{}) {
	data := make(log.Data, 2)
	data["loglevel"] = logLevel
	if event != "" {
		data["event"] = event
	}
	log.DataPrintf(data, format, args...)
	fmt.Printf(time.Now().Format("2006-01-02 15:04:05 ")+format+"\n", args...)
}

func (self *RunningTask) LogProcess(buf []byte) {
	log.DataPrintf(log.Data{"program": "mcloud_task_process"}, string(buf))
}

func (self *RunningTask) Stopped() bool {
	self.mu.RLock()
	defer self.mu.RUnlock()

	return self.stopped
}

func (self *RunningTask) command(name string, arg ...string) *exec.Cmd {
	var cmd *exec.Cmd
	cmd = exec.Command(name, arg...)
	cmd.Dir = self.Dir
	cmd.SysProcAttr = &syscall.SysProcAttr{
		Setsid: true,
		Credential: &syscall.Credential{
			Uid:         self.execUid,
			Gid:         self.execGid,
			NoSetGroups: true,
		},
	}

	return cmd
}

func (self *RunningTask) Prepare() error {
	if self.prepared {
		panic("already prepared")
	}
	if !self.flocked {
		panic("cannot prepare unlocked")
	}
	var err error

	// clean the directory before using it
	err = common.RemoveDirContents(self.Dir)
	if err != nil {
		return err
	}

	err = self.rotateLog(self.logPath)
	if err != nil {
		return err
	}

	// get our PID (in the parent namespace)
	pid := strconv.Itoa(os.Getpid())
	/*var pidFile *os.File
	pidFile, err = os.Create(self.Dir + "/task.pid")
	if err != nil {
		return err
	}
	_, err = pidFile.WriteString(pid)
	if err != nil {
		pidFile.Close()
		return err
	}
	pidFile.Close()
	self.Log(log.LOG_DEBUG, "our pid is %s", pid)*/

	// mount the correct PID namespace
	err = syscall.Mount("proc", "/proc", "proc", 0, "")
	if err != nil {
		return err
	}

	// create a cgroup and add this process to its tasks
	cgroupPrefix, err := cgroup.GetSubSysMountPoint("cpuacct")
	if err != nil {
		return err
	}
	cgroupDir := "/mcloud/" + self.Id
	self.cgroupPath = cgroupPrefix + cgroupDir
	self.cgroup = cgroup.NewCgroup(cgroupDir)
	self.cpuacct = self.cgroup.AddController("cpuacct")
	self.cgroup.SetPermissions(cgroup.Mode(0o770), cgroup.Mode(0o770), cgroup.Mode(0o770))
	err = self.cgroup.Create()
	if err != nil {
		return err
	}
	var procsFile *os.File
	procsFile, err = os.OpenFile(self.cgroupPath+"/cgroup.procs", os.O_WRONLY, 0o644)
	if err != nil {
		return err
	}
	_, err = procsFile.WriteString(pid)
	if err != nil {
		procsFile.Close()
		return err
	}
	procsFile.Close()

	var notifyFile *os.File
	notifyFile, err = os.OpenFile(self.cgroupPath+"/notify_on_release", os.O_WRONLY, 0o644)
	if err != nil {
		return err
	}
	_, err = notifyFile.WriteString("1")
	if err != nil {
		notifyFile.Close()
		return err
	}
	notifyFile.Close()

	// link files of this class
	ex, err := common.Exists(filepath.Join(self.Dir, "class"))
	if err != nil {
		return err
	}
	if !ex {
		err = os.Symlink(
			filepath.Join(self.Conf.ClassDir, *self.Task.Class),
			filepath.Join(self.Dir, "class"),
		)
		if err != nil {
			return err
		}
	}

	// output directories and symlinks setup
	err = os.MkdirAll(filepath.Join(self.Dir, "outputs"), 0o755)
	if err != nil {
		return err
	}

	outputVariables := map[string]string{
		"%{d}": filepath.Join(self.Conf.OutputsDir, common.TaskOutputDurableDir),
		"%{e}": filepath.Join(self.Conf.OutputsDir, common.TaskOutputEphemeralDir),
		"%p":   filepath.Join(self.Conf.OutputsDir, common.TaskOutputEphemeralDir),
	}
	self.Outputs, err = processOutputs(self.Specification[ClassStart], self.DirSuffix, outputVariables)
	if err != nil {
		return err
	}
	var processedSpecification string
	startSpecification := string(self.Specification[ClassStart])
	if startSpecification != "" {
		processedSpecification = OutputRegexp.ReplaceAllStringFunc(startSpecification, processOutputPaths(self.DirSuffix, outputVariables))
		if processedSpecification == "" {
			return fmt.Errorf("failed to replace output directories in the json specification: %s", startSpecification)
		}
	}
	self.Specification[ClassStart] = json.RawMessage(processedSpecification)

	err = self.prepareOutputs()
	if err != nil {
		return err
	}

	self.prepared = true

	go self.rotator()
	go self.monitor()

	return nil
}

type fileTime struct {
	path  string
	mtime time.Time
}

// Redirect stderr and stdout of this process to a logfile.
func (self *RunningTask) rotateLog(logPath string) (err error) {
	// if there already is a log file, archive it
	var ex bool
	ex, err = common.Exists(logPath)
	if err != nil {
		return
	}

	if ex {
		newPath := fmt.Sprintf("%s-%s", logPath, time.Now().Format(time.RFC3339))
		err = os.Rename(logPath, newPath)
		if err != nil {
			return
		}

		defer func() {
			var err error
			err = self.cleanOldLogs(logPath)
			if err != nil {
				self.Log(log.LOG_ERROR, "failed to clean old log files %s", err)
			}
		}()
	}

	// create a new file and use its file descriptor
	var new *os.File
	new, err = os.OpenFile(logPath, os.O_WRONLY|os.O_CREATE|os.O_APPEND|os.O_SYNC, 0o644)
	if err != nil {
		return err
	}
	err = syscall.Dup2(int(new.Fd()), int(os.Stdout.Fd()))
	if err != nil {
		return err
	}
	err = syscall.Dup2(int(new.Fd()), int(os.Stderr.Fd()))
	if err != nil {
		return err
	}

	return
}

func (self *RunningTask) cleanOldLogs(logPath string) (err error) {
	var files []string
	files, err = filepath.Glob(fmt.Sprintf("%s-*", logPath))
	if err != nil {
		err = fmt.Errorf("failed to glob: %w", err)
		return
	}

	fts := make([]fileTime, 0, len(files))
	for _, file := range files {
		var fi os.FileInfo
		fi, err = os.Stat(file)
		if err != nil {
			err = fmt.Errorf("failed to stat: %w", err)
			return
		}
		if !fi.Mode().IsRegular() {
			continue
		}

		fts = append(fts, fileTime{
			path:  file,
			mtime: fi.ModTime(),
		})
	}
	if len(fts) <= OldTaskLogBackups {
		return
	}
	sort.Slice(fts, func(i, j int) bool {
		return fts[i].mtime.After(fts[j].mtime)
	})
	for i := 0; i < len(fts)-OldTaskLogBackups; i++ {
		err = os.Remove(fts[i].path)
		if err != nil {
			self.Log(log.LOG_ERROR, "failed to stat: %s", err)
			continue
		}
		self.Log(log.LOG_DEBUG, "removed old log file: %s", fts[i].path)
	}

	return
}

func (self *RunningTask) Start() (err error) {
	if EnableGoClasses {
		err = self.yaegiLoad(filepath.Join(self.Dir, "class"))
		if err != nil {
			return
		}
	}

	binary := self.Dir + "/class/" + ClassStart
	args := []string{self.Dir + "/outputs", string(self.Specification[ClassStart])}
	args = append(args, string(self.Task.SelectedResources.Json()))
	if self.privateData != nil {
		args = append(args, *self.privateData)
	}

	// setup stdout and stderr for the inner processes
	stdout := &Logger{
		lumberjack.Logger{
			Filename:   self.Dir + "/stdout",
			MaxSize:    StdoutRotationLimit,
			MaxBackups: OldStdoutBackups,
		},
		self,
		nil,
	}

	defer stdout.Close()
	defer stdout.Flush()
	stderr := &Logger{
		lumberjack.Logger{
			Filename:   self.Dir + "/stderr",
			MaxSize:    StderrRotationLimit,
			MaxBackups: OldStderrBackups,
		},
		self,
		nil,
	}
	defer stderr.Close()
	defer stderr.Flush()

	cmd := self.command(binary, args...)
	cmd.Stdout = stdout
	cmd.Stderr = stderr
	cmd.WaitDelay = TimeoutExit

	self.mu.Lock()
	self.setState(StateStarting)

	self.startTime = time.Now()
	self.cmd = cmd

	self.cond.Broadcast()
	self.mu.Unlock()

	defer func() {
		self.mu.Lock()

		self.cmd = nil
		self.sid = 0
		self.cond.Broadcast()

		self.mu.Unlock()
	}()

	defer self.Kill()
	defer self.Snapshot()
	err = cmd.Start()
	if err != nil {
		return
	}

	var sid int64
	sid, err = GetSid(cmd.Process.Pid)
	if err != nil {
		return
	}
	self.mu.Lock()
	self.sid = sid
	self.mu.Unlock()

	self.Log(log.LOG_DEBUG, "class processes running")

	err = cmd.Wait()
	if err != nil {
		var exitErr *exec.ExitError
		if errors.As(err, &exitErr) {
			err = fmt.Errorf("non-zero exit code: %d", exitErr.ExitCode())
			return
		}

		if errors.Is(err, exec.ErrWaitDelay) {
			self.Log(log.LOG_ERROR, "ignoring waitdelay error")
			err = nil
		}

		var syscallErr *os.SyscallError
		if errors.As(err, &syscallErr) && syscallErr.Syscall == "waitid" && syscallErr.Err == syscall.ECHILD {
			err = nil
		}
	}
	if err != nil {
		self.Log(log.LOG_ERROR, "unknown process error: %s", err)
		return
	}

	return
}

func (self *RunningTask) RunHook(name string, args ...string) (output string, code int, err error) {
	defer func() {
		r := recover()
		if r != nil {
			err = fmt.Errorf("panic: %v", r)
		}
		if err != nil {
			self.Log(log.LOG_ERROR, "running of hook %s failed: %s", name, err)
		}
	}()

	pth := filepath.Join(self.Dir, "class", name) // TODO store in self

	var useGo bool
	if EnableGoClasses {
		goPth := pth + ".go"
		_, err = os.Stat(goPth)
		if err != nil {
			if !os.IsNotExist(err) {
				return
			}
		} else {
			useGo = true
		}
	}
	if !useGo {
		_, err = os.Stat(pth)
		if err != nil {
			if !os.IsNotExist(err) {
				return
			}
			err = nil
			code = classes.CheckRunning
			return // class has no such hook //TODO err
		}
	}

	if !useGo {
		var arguments []string
		arguments = append(arguments, self.Dir+"/outputs") // TODO use only working directory
		arguments = append(arguments, string(self.Specification[name]))
		arguments = append(arguments, args...)

		var out []byte
		cmd := self.command(pth, arguments...)
		out, err = cmd.CombinedOutput()
		out = bytes.TrimSuffix(out, []byte("\n"))
		output = string(out)
		if err != nil {
			exitErr, ok := err.(*exec.ExitError)
			if !ok {
				return
			}

			code = exitErr.Sys().(syscall.WaitStatus).ExitStatus()
			err = nil
			return
		}

		code = classes.CheckRunning
		return
	}

	var fni interface{}
	fni, err = self.yaegiFn(name)
	if err != nil {
		return
	}

	fn, ok := fni.(func(binDir string, outputDir string, specificationJson []byte, args []string) (code int, info string, err error))
	if !ok {
		err = fmt.Errorf("class '%s' has wrong function type: %s",
			*self.Task.Class, reflect.TypeOf(fn),
		)
		return
	}

	miscDir := filepath.Join(self.Dir, "class/misc", name)
	outputsDir := filepath.Join(self.Dir, "outputs")
	specification := self.Specification[name]
	if string(specification) == "" {
		specification = json.RawMessage("{}")
	}
	code, output, err = fn(
		miscDir,
		outputsDir,
		specification,
		args,
	)
	output = strings.TrimSuffix(output, "\n")
	if err != nil {
		return
	}

	return
}

func (self *RunningTask) GetPids() ([]int, error) {
	data, err := os.ReadFile(self.cgroupPath + "/cgroup.procs")
	if err != nil {
		return nil, err
	}

	var pids []int
	for _, _pid := range strings.Fields(string(data)) {
		pid, err := strconv.ParseInt(_pid, 10, 64)
		if err != nil {
			self.Log(log.LOG_ERROR, "ERROR parsing pid '%s' in a cgroup tasks file: %s", _pid, err)
			continue
		}
		pids = append(pids, int(pid))
	}

	return pids, nil
}

func GetSid(pid int) (int64, error) {
	ps, err := linuxproc.ReadProcessStat(fmt.Sprintf("/proc/%d/stat", pid))
	if err != nil {
		return -1, err
	}

	return ps.Session, nil
}

func (self *RunningTask) Signal(signal syscall.Signal) error {
	var sid int64
	var err error

	self.mu.RLock()
	if self.cmd == nil || self.cmd.Process == nil {
		self.mu.RUnlock()
		return fmt.Errorf("process not running")
	}
	sid = self.sid
	self.mu.RUnlock()

	var pids []int
	pids, err = self.GetPids()
	if err != nil {
		return err
	}

	var sameSessionPids []int
	sameSessionPids = make([]int, 0, len(pids))
	for _, opid := range pids {
		osid, err := GetSid(opid)
		if err != nil {
			continue
		}
		if osid == sid {
			sameSessionPids = append(sameSessionPids, opid)
		}
	}
	if len(sameSessionPids) == 0 {
		return nil
	}

	self.Log(log.LOG_DEBUG, "sending signal '%s' to session id %d %v", signal, sid, sameSessionPids)
	for _, opid := range sameSessionPids {
		err = syscall.Kill(opid, signal)
		if err != nil {
			self.Log(log.LOG_ERROR, "ERROR sending signal '%s' to pid '%d': %s", signal, opid, err)
			continue
		}
	}
	return err
}

func (self *RunningTask) Stop() error {
	self.mu.Lock()
	if self.stopped {
		self.mu.Unlock()
		return nil
	}
	self.stopped = true
	self.mu.Unlock()

	go func() {
		time.Sleep(30 * time.Second)
		self.Log(log.LOG_ERROR, "ERROR stopping task: timed out")

		os.Exit(200)
	}()

	select {
	case self.SkipTimeout <- true:
	default:
	}

	return self.Kill()
}

func (self *RunningTask) Kill() error {
	var err error

	terminated := make(chan bool, 1)

	self.mu.RLock()
	startTime := self.startTime
	self.mu.RUnlock()
	go func() {
		defer func() {
			terminated <- true
		}()
		exited := make(chan bool, 1)
		timeout := time.NewTimer(TimeoutExit)
		defer timeout.Stop()

		go func() {
			self.mu.RLock()
			for self.cmd != nil && self.startTime == startTime {
				self.cond.Wait()
			}
			exited <- true
			self.mu.RUnlock()
		}()
		select {
		case <-exited:
		case <-timeout.C:
			self.Log(log.LOG_DEBUG, "process kill timed out, stopping")
			self.Stop()
		}
	}()

	err = self.Signal(syscall.SIGTERM)
	if err != nil {
		return err
	}

	useKill := time.NewTimer(TimeoutStop)
	defer useKill.Stop()

	select {
	case <-terminated:
	case <-useKill.C:
		self.Log(log.LOG_DEBUG, "using KILL signal")
		err = self.Signal(syscall.SIGKILL)
		if err != nil {
			return err
		}
	}

	return nil
}

func (self *RunningTask) storeSnapshot(target string, startTime time.Time) error {
	now := time.Now().Unix()
	dir := fmt.Sprintf("%s/%012d__%s_%d-%d", self.Conf.SnapshotDir, now, self.Id, startTime.Unix(), now)
	self.Log(log.LOG_DEBUG, "storing task directory to: %s", dir)

	flock, err := flock.FlockFileTimeout(self.Conf.SnapshotDir, false, 1*time.Second)
	if err != nil {
		return err
	}

	err = copy.Copy(self.Dir, dir)
	if err != nil {
		_ = flock.FUnlock()
		return fmt.Errorf("copying to %s: %w", dir, err)
	}
	err = flock.FUnlock()
	if err != nil {
		return err
	}

	err = self.cleanSnapshots(self.Conf.MaxSnapshots)
	if err != nil {
		return err
	}

	return nil
}

func (self *RunningTask) cleanSnapshots(keep int) error {
	flock, err := flock.FlockFileTimeout(self.Conf.SnapshotDir, true, 1*time.Second)
	if err != nil {
		return nil // ignore error
	}
	defer flock.FUnlock()

	files, err := os.ReadDir(self.Conf.SnapshotDir)
	if err != nil {
		return err
	}

	if len(files) <= keep-1 {
		return nil
	}
	sort.Slice(files, func(i, j int) bool {
		return files[i].Name() < files[j].Name()
	})
	files = files[:len(files)-keep-1]

	for _, file := range files {
		target := fmt.Sprintf("%s/%s", self.Conf.SnapshotDir, file.Name())
		self.Log(log.LOG_DEBUG, "deleting snapshot: %s", target)
		err = os.RemoveAll(target)
		if err != nil {
			self.Log(log.LOG_ERROR, "ERROR: failed to delete snapshot: %s", err)
		}
	}

	return nil
}

func (self *RunningTask) Snapshot() error {
	var target string
	var startTime time.Time

	self.mu.RLock()
	if self.state(StateFailed) {
		target = "failed"
	} else if self.state(StateDone) {
		target = "finished"
	}
	startTime = self.startTime
	self.mu.RUnlock()

	if target != "" {
		err := self.storeSnapshot(target, startTime)
		if err != nil {
			self.Log(log.LOG_ERROR, "ERROR storing task directory snapshot: %s", err)
		}
	}
	return nil
}

func (self *RunningTask) Delete() error {
	_ = self.leaveOutputs()

	var err error
	if self.res.cpuCounter != nil {
		self.res.cpuCounter.Stop()
	}

	if !self.flocked {
		return nil
	}

	self.Log(log.LOG_DEBUG, "deleting cgroup")
	err = self.cgroup.DeleteExt(cgroup.DeleteRecursive | cgroup.DeleteIgnoreMigration)
	if err != nil {
		self.Log(log.LOG_ERROR, "error deleting cgroup: %s", err)
	}

	for _, output := range self.Outputs {
		err = os.RemoveAll(output.New)
		if err != nil {
			self.Log(log.LOG_ERROR, "error deleting output directory: %s", err)
		}
		err = os.RemoveAll(output.NewTmp)
		if err != nil {
			self.Log(log.LOG_ERROR, "error deleting tmp output directory: %s", err)
		}

	}

	self.LogEvent(log.LOG_INFO, "stop", "deleting task directory")
	err = os.RemoveAll(self.Dir)
	if err != nil {
		self.Log(log.LOG_ERROR, "error deleting task directory: %s", err)
	}

	self.SetState(StateDeleted)
	self.StateSender.Wait()

	self.enc.Close()

	return nil
}

func (self *RunningTask) State(state string) bool {
	self.mu.RLock()
	defer self.mu.RUnlock()

	return self.state(state)
}

func (self *RunningTask) state(state string) bool {
	return self.state_ == state
}

func (self *RunningTask) SetState(state string) {
	self.mu.Lock()
	defer self.mu.Unlock()

	self.setState(state)
}

func (self *RunningTask) setState(state string) {
	oldState := self.state_
	self.state_ = state

	if oldState != "" {
		self.Log(log.LOG_WARN, "state change: %s -> %s", oldState, state)
	}

	if state != StateRunning {
		self.runningSinceTime = nil
	}

	var err error
	if oldState != StateRunning && self.state(StateRunning) {
		self.StateBump()

		err = self.takeOutputs()
		if err != nil {
			self.Log(log.LOG_ERROR, "ERROR taking outputs: %s", err)
			go self.Stop()
		}
		now := time.Now()
		self.runningSinceTime = &now
	}
	if self.takenOutputs && !(self.state(StateRunning) || self.state(StateDone)) {
		// TODO wait for it
		err = self.leaveOutputs()
		if err != nil {
			self.Log(log.LOG_ERROR, "ERROR leaving outputs: %s", err)
			go self.Stop()
		}
	}

	if !self.state(StateDeleted) {
		err = os.WriteFile(self.Dir+"/state", []byte(state), 0o644)
		if err != nil {
			self.Log(log.LOG_ERROR, "error ERROR /state file: %s", err)
		}
	}

	self.StateBump()
}

func (self *RunningTask) getState() (interface{}, bool) {
	return self.getDbNodeTask(), self.State(StateDeleted)
}

func (self *RunningTask) getDbNodeTask() common.DbNodeTask {
	var err error
	var filled bool

	usage := common.NewMultiResources()

	if !self.State(StateDeleted) {
		if self.res.cpuCounter != nil {
			filled, err = self.res.cpuCounter.Filled()
			if err != nil {
				self.Log(log.LOG_ERROR, "cpuCounter ERROR: %s", err)
			} else if filled {
				avg, _ := self.res.cpuCounter.GetAverage(15)
				usage["CPU"] = []float64{avg / 10000000}
			}
		}
		numOutputs := len(self.Outputs)

		if numOutputs > 0 && self.res.usedDiskSpaceCounter != nil {
			filled, err = self.res.usedDiskSpaceCounter.Filled()
			if err != nil {
				self.Log(log.LOG_ERROR, "usedDiskSpaceCounter ERROR: %s", err)
			} else if filled {
				tmp, _ := self.res.usedDiskSpaceCounter.GetLast()
				usage["disk"] = []float64{float64(tmp) / (1024 * 1024)}
			}
		}
		if self.res.usedMemoryCounter != nil {
			filled, err = self.res.usedMemoryCounter.Filled()
			if err != nil {
				self.Log(log.LOG_ERROR, "usedMemoryCounter ERROR: %s", err)
			} else if filled {
				tmp, _ := self.res.usedMemoryCounter.GetMax(3)
				usage["mem"] = []float64{float64(tmp) / 1024}
			}
		}
		if self.res.gpu.count > 0 {
			var tmp uint64
			for _, card := range self.res.gpu.counters {
				// gpu
				filled, err = card.usedGPU.Filled()
				if err != nil {
					// self.Log(log.LOG_ERROR, "GPU usedGPU ERROR: %s", err)
				} else if filled {
					tmp, _ = card.usedGPU.GetLast()
					usage["GPU"] = append(usage["GPU"], float64(tmp))
				}

				// gpu memory
				filled, err = card.usedMemory.Filled()
				if err != nil {
					self.Log(log.LOG_ERROR, "GPU usedMemory ERROR: %s", err)
				} else if filled {
					tmp, _ = card.usedMemory.GetLast()
					usage["GPU_mem"] = append(usage["GPU_mem"], float64(tmp))
				}
			}
			if len(usage["GPU"]) != len(usage["GPU_mem"]) {
				// we only have partial information because of GPU accounting not being enabled
				delete(usage, "GPU")
			}
		}

		if self.State(StateRunning) {
			var out string

			out, _, err = self.RunHook(ClassData)
			if err == nil && len(out) > 0 {
				tmp := out
				self.privateData = &tmp
			}

			out, _, err = self.RunHook(ClassStatus)
			if err == nil && len(out) > 0 {
				self.status = out
			}
		}
	}

	var lastRunningTime *time.Time
	now := time.Now()
	if self.State(StateRunning) {
		lastRunningTime = &now
	}

	self.mu.RLock()
	nodeTask := common.DbNodeTask{
		Id:                self.Id,
		Task:              self.Task.DbId,
		Node:              self.Node,
		Revision:          self.Task.Revision,
		State:             self.state_,
		Warnings:          self.warnings,
		StartTime:         &self.startTime,
		CreationTime:      self.CreationTime,
		LastOnlineTime:    &now,
		LastRunningTime:   lastRunningTime,
		SelectedResources: self.Task.SelectedResources,
		ResourceUsage:     usage,
		OriginTask:        self.Task,
		ApiData:           self.apiData,
		PrivateData:       self.privateData,
		Status:            self.status,
		RunningSinceTime:  self.runningSinceTime,
	}
	self.mu.RUnlock()

	return nodeTask
}

func (self *RunningTask) rotator() {
	ticker := time.NewTicker(15 * time.Second)
	defer ticker.Stop()

	for {
		<-ticker.C
		if self.Stopped() {
			break
		}

		var err error
		var fi os.FileInfo
		fi, err = os.Stat(self.logPath)
		if err != nil {
			self.Log(log.LOG_ERROR, "failed to rotate log: %s", err)
			continue
		}
		if fi.Size() < TaskLogRotationLimit*1024*1024 {
			continue
		}

		err = self.rotateLog(self.logPath)
		if err != nil {
			self.Log(log.LOG_ERROR, "failed to rotate log: %s", err)
			continue
		}

		self.Log(log.LOG_INFO, "log file %s rotated", self.logPath)
	}
}

func (self *RunningTask) monitor() { // TODO stop this before stopping the task
	ticker := time.NewTicker(1 * time.Second) // TODO constant
	defer ticker.Stop()

	for {
		<-ticker.C
		if self.Stopped() {
			break
		}

		if !(self.State(StateStarting) || self.State(StateRunning)) {
			continue
		}

		checkOutput, checkCode, err := self.RunHook(ClassCheck)
		if len(checkOutput) > 0 {
			self.Log(log.LOG_WARN, "(check hook) %s", checkOutput)
		}
		if err != nil {
			continue
		}

		self.mu.Lock()
		if !(self.state(StateStarting) || self.state(StateRunning)) {
			self.mu.Unlock()
			continue
		}

		self.warnings = string(checkOutput)
		switch checkCode {
		case classes.CheckRunning:
			if self.state(StateStarting) {
				self.setState(StateRunning)
			}
		case classes.CheckBroken, classes.CheckEmpty:
			if checkCode == classes.CheckEmpty && !self.state(StateRunning) {
				break
			}
			self.Log(log.LOG_WARN, "terminating processeses based on the check script result (code=%d)", checkCode)
			go func() {
				var err error
				err = self.Kill()
				if err != nil {
					self.Log(log.LOG_ERROR, "error killing processes: %s", err)
					self.Stop()
				}
			}()
		case classes.CheckUnknown:
			self.Log(log.LOG_DEBUG, "check script returned 'unknown' result (taking no action)")
		default:
			self.Log(log.LOG_ERROR, "(check hook) unmapped exit code: %d", checkCode)
		}

		self.mu.Unlock()
	}
}

func (self *RunningTask) getCPUTicks() (uint64, error) {
	data, err := os.ReadFile(self.cgroupPath + "/cpuacct.usage")
	if err != nil {
		self.Log(log.LOG_ERROR, "error: %s", err)
		return 0, err
	}
	num, err := strconv.ParseUint(strings.TrimSuffix(string(data), "\n"), 10, 64)
	if err != nil {
		self.Log(log.LOG_ERROR, "error %s", err)
		return 0, err
	}
	return num, nil
}

// Returns true the other NodeTask has precedence and this one should be stopped.
func (self *RunningTask) handleDuplicate(otherNodeTask *common.DbNodeTask) (stop bool) {
	if otherNodeTask.State != StateRunning {
		return
	}
	self.Log(log.LOG_INFO, "duplicate running detected: %s", otherNodeTask.Id)

	if self.Task.Revision != otherNodeTask.Revision {
		if self.Task.Revision < otherNodeTask.Revision {
			self.Log(log.LOG_INFO, "duplicate task at %s has higher revision, stopping", otherNodeTask.Node)
			stop = true
		}
		return
	}

	// same revision of the same alternative
	if (self.Task.Alternative == nil && otherNodeTask.OriginTask.Alternative == nil) ||
		(self.Task.Alternative != nil && otherNodeTask.OriginTask.Alternative != nil && *self.Task.Alternative == *otherNodeTask.OriginTask.Alternative) {
		if self.Task.Finite {
			if otherNodeTask.StartTime.Before(self.startTime) {
				self.Log(log.LOG_INFO, "duplicate task at %s started sooner, stopping", otherNodeTask.Node)
				stop = true
			}
		} else {
			if otherNodeTask.CreationTime.After(self.CreationTime) {
				self.Log(log.LOG_INFO, "duplicate task at %s started later, stopping", otherNodeTask.Node)
				stop = true
			}
		}
		return
	}

	// same revision of different alternatives

	if ((self.Task.Priority == nil && otherNodeTask.OriginTask.Priority != nil) ||
		(self.Task.Priority != nil && otherNodeTask.OriginTask.Priority == nil)) ||
		(self.Task.Priority != nil && otherNodeTask.OriginTask.Priority != nil && *self.Task.Priority != *otherNodeTask.OriginTask.Priority) {
		// non-nil priority has precedence
		if self.Task.Priority == nil || (otherNodeTask.OriginTask.Priority != nil && *otherNodeTask.OriginTask.Priority > *self.Task.Priority) {
			self.Log(log.LOG_INFO, "duplicate task at %s has higher priority, stopping", otherNodeTask.Node)
			stop = true
		}
		return
	}
	if otherNodeTask.OriginTask.Level != self.Task.Level {
		if otherNodeTask.OriginTask.Level < self.Task.Level {
			self.Log(log.LOG_INFO, "duplicate task at %s has lower level, stopping", otherNodeTask.Node)
			stop = true
		}
		return
	}
	if otherNodeTask.OriginTask.Order != self.Task.Order {
		if otherNodeTask.OriginTask.Order < self.Task.Order {
			self.Log(log.LOG_INFO, "duplicate task at %s has lower order, stopping", otherNodeTask.Node)
			stop = true
		}
		return
	}

	return
}

func (self *RunningTask) listen() {
	self.enc.Subscribe(fmt.Sprintf("task_state.%s", mNats.EscapeRoutingKey(self.Task.Id)), func(dbNodeTask common.DbNodeTask) {
		if self.Stopped() {
			return
		}
		if dbNodeTask.Id != self.Id {
			stop := self.handleDuplicate(&dbNodeTask)
			if stop {
				err := self.Stop()
				if err != nil {
					self.Log(log.LOG_ERROR, "error stopping task: %s", err)
				}
			}
		}
	})
	self.enc.Subscribe("controller_state.*", func(dbController *common.DbController) {
		if dbController.State_ == ControllerStateMaster {
			self.mu.Lock()
			self.lastOnlineMasterController = dbController.LastOnline
			self.mu.Unlock()
		}
	})
	self.enc.Subscribe(fmt.Sprintf("task_command.%s", self.Task.Id), func(command string) {
		switch command {
		case "terminate":
			if self.Stopped() {
				return
			}
			self.Log(log.LOG_INFO, "received a terminate command from a controller")
			err := self.Stop()
			if err != nil {
				self.Log(log.LOG_ERROR, "error stopping task: %s", err)
			}
		default:
			self.Log(log.LOG_WARN, "unknown remote command: %s", command)
		}
	})
	self.enc.Subscribe(fmt.Sprintf("task_confirmation.%s", mNats.EscapeRoutingKey(self.Id)), func(_ interface{}) {
		select {
		case self.confirmed <- true:
		default:
		}
	})
}

type Logger struct {
	lumberjack.Logger
	runningTask *RunningTask
	lineBuffer  []byte
}

func (self *Logger) Write(buf []byte) (n int, err error) {
	self.lineBuffer = append(self.lineBuffer, buf...)

	for {
		index := bytes.Index(self.lineBuffer, []byte("\n"))
		if index == -1 {
			break
		}
		if index > 0 { // ignore empty lines
			self.runningTask.LogProcess(self.lineBuffer[:index+1])
			self.Logger.Write(
				[]byte(fmt.Sprintf("%s %s", time.Now().Format("2006-01-02 15:04:05"), self.lineBuffer[:index+1])),
			)
		}
		self.lineBuffer = self.lineBuffer[index+1:]
	}

	return len(buf), nil // ignore errors
}

func (self *Logger) Flush() (n int, err error) {
	len := len(self.lineBuffer)
	if len > 0 {
		self.runningTask.LogProcess(self.lineBuffer)
		self.Logger.Write(
			[]byte(fmt.Sprintf("%s %s", time.Now().Format("2006-01-02 15:04:05"), self.lineBuffer)),
		)
		self.lineBuffer = self.lineBuffer[:0]
	}

	return len, nil
}

func (self *RunningTask) usedGPUMemoryFunc(index int) counter.SourceFunc {
	return func() (uint64, error) {
		self.res.mu.Lock()
		defer self.res.mu.Unlock()

		if self.res.gpu.current == nil || len(self.res.gpu.current.GPUs) <= index {
			return 0, fmt.Errorf("no such GPU at index %d", index)
		}

		pids, err := self.GetPids()
		if err != nil {
			return 0, err
		}

		var used uint64
		for _, process := range self.res.gpu.current.GPUs[index].Processes {
			for _, pid := range pids {
				if process.Pid != pid {
					continue
				}
				used += uint64(process.UsedMemory)
			}
		}

		return used, nil
	}
}

func (self *RunningTask) usedGPUFunc(index int) counter.SourceFunc {
	return func() (uint64, error) {
		self.res.mu.Lock()
		defer self.res.mu.Unlock()

		if self.res.gpu.current == nil || len(self.res.gpu.current.GPUs) <= index {
			return 0, fmt.Errorf("no such GPU at index %d", index)
		}

		pids, err := self.GetPids()
		if err != nil {
			return 0, err
		}

		var used uint64
		var numProc int
		for _, process := range self.res.gpu.current.GPUs[index].Processes {
			for _, pid := range pids {
				if process.Pid != pid {
					continue
				}
				numProc++
			}
		}
		for _, process := range self.res.gpu.current.GPUs[index].AccountedProcesses {
			for _, pid := range pids {
				if process.Pid != pid {
					continue
				}
				used += uint64(process.GpuUtil)
				numProc--
			}
		}
		if numProc != 0 {
			return 0, fmt.Errorf("missing accounted gpu processes")
		}

		return used, nil
	}
}

func (self *RunningTask) GetOutputDirectoryUsedSpace() (uint64, error) {
	var size uint64
	var err error
	for _, output := range self.Outputs {
		err = filepath.Walk(output.Original, func(_ string, info os.FileInfo, err error) error {
			if err != nil {
				return err
			}
			if !info.IsDir() {
				size += uint64(info.Size())
			}
			return nil
		})
	}
	return size, err
}

func (self *RunningTask) GetUsedMemory() (uint64, error) {
	/* TODO: use cgroup memory controller for this
	 * at this moment it is disabled in the Debian kernel because of its overhead*/
	pids, err := self.GetPids()
	if err != nil {
		return 0, err
	}

	var used uint64
	for _, pid := range pids {
		f, err := os.Open(fmt.Sprintf("/proc/%d/smaps", pid))
		if err != nil {
			return 0, err
		}

		r := bufio.NewScanner(f)
		for r.Scan() {
			line := r.Bytes()
			if bytes.HasPrefix(line, []byte("Pss:")) {
				var size uint64
				_, err = fmt.Sscanf(string(line[4:]), "%d", &size)
				if err != nil {
					f.Close()
					return 0, err
				}
				used += size
			}
		}
		err = r.Err()
		if err != nil {
			f.Close()
			return 0, err
		}
		f.Close()
	}
	return used, nil
}

func (self *RunningTask) ControllerOnline() bool {
	self.mu.RLock()
	defer self.mu.RUnlock()

	return self.lastOnlineMasterController.Add(30 * time.Second).After(time.Now())
}

func (self *RunningTask) Run() (err error) {
	defer self.Delete()

	err = self.Prepare()
	if err != nil {
		err = fmt.Errorf("preparing: %w", err)
		return
	}

	var firstRun bool
	var errorCounter int

	firstRun = true
	for {
		err = self.ResetDir()
		if err != nil {
			self.Log(log.LOG_ERROR, "ERROR: failed to reset directory, stopping: %s", err)
			break
		}

		run := true
		if firstRun {
			firstRun = false
			if self.Conf.StartDelay > 0 {
				self.Log(log.LOG_DEBUG, "sleeping %d ms", self.Conf.StartDelay)

				timeout := make(chan bool, 1)
				go func() {
					time.Sleep(time.Duration(self.Conf.StartDelay) * time.Millisecond)
					timeout <- true
				}()
				select {
				case <-timeout:
				case <-self.SkipTimeout:
					run = false
					err = fmt.Errorf("cancelled")
				}
			}
		}

		if run {
			timer := time.AfterFunc(common.TaskRunningDeadline, func() {
				if !self.State(StateStarting) {
					errorCounter = 0
					return
				}

				self.Log(log.LOG_WARN, "task failed to reach '%s' state within %s, killing", StateRunning, common.TaskRunningDeadline)
				err := self.Kill()
				if err != nil {
					self.Log(log.LOG_ERROR, "failed to kill stalled task")
					self.Stop()
				}
			})

			self.LogEvent(log.LOG_INFO, "process_start", "process start")
			err = self.Start()
			timer.Stop()
			self.Log(log.LOG_INFO, "process exited")

			self.Kill()
		}

		if self.Task.Finite {
			var out string
			if err == nil {
				out, _, err = self.RunHook(ClassSuccess)
				if err != nil {
					// TODO log
				} else {
					self.SetState(StateDone)
					self.Log(log.LOG_INFO, "process exited successfully")
				}
			}
			if err != nil {
				self.SetState(StateFailed)
				var err2 error
				out, _, err2 = self.RunHook(ClassFailure)
				if err2 != nil {
					// TODO log
				}

				self.Log(log.LOG_INFO, "finite process error: %s", err)
			}

			self.apiData = out
			self.Log(log.LOG_DEBUG, "waiting for controller confirmation")
			<-self.confirmed
			self.Log(log.LOG_DEBUG, "confirmed")
			break
		}

		// non-finite tasks exiting is always a failure
		self.RunHook(ClassFailure)
		self.SetState(StateFailed)
		errorCounter += 1

		if self.Stopped() {
			self.Log(log.LOG_WARN, "task was stopped intentionally, stopping")
			break
		} else {
			self.LogEvent(log.LOG_WARN, "process_crash", "process exited unexpectedly: %v", err)
			if self.ControllerOnline() {
				self.Log(log.LOG_INFO, "a master controller is online, stopping task")
				break
			}
		}

		if errorCounter > 1 {
			timeout := make(chan bool, 1)
			go func(errorCounter int) {
				time.Sleep(time.Duration((errorCounter-1)*2) * time.Second)
				timeout <- true
			}(errorCounter)
			select {
			case <-timeout:
			case <-self.SkipTimeout:
			}
		}
	}
	return
}
