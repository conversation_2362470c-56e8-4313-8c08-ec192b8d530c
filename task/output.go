package task

import (
	"errors"
	"fmt"
	"os"
	"path/filepath"
	"regexp"
	"sort"
	"strings"
	"time"

	"git.moderntv.eu/go/common"
	"git.moderntv.eu/mcloud/log"
	"git.moderntv.eu/mcloud/system/pkg/flock"
)

var OutputRegexp = regexp.MustCompile(`(^|[^\w-])(%\{[ed]\}|%p)([^|"'\[\]]+)`)

type Output struct {
	Original string
	NewFile  string
	New      string
	NewTmp   string
}

func processOutput(outputPath string, dirSuffix string, variables map[string]string) (output Output) {
	matches := OutputRegexp.FindStringSubmatch(outputPath)
	if len(matches) == 4 {
		value, ok := variables[matches[2]]
		if ok {
			outputPath = matches[1] + value + matches[3]
		}
	}

	var filename string
	if !strings.HasSuffix(outputPath, "/") {
		filename = filepath.Base(outputPath)
	}

	outDir := filepath.Dir(outputPath)
	outDirPath := filepath.Dir(outDir)
	outDirName := filepath.Base(outDir)

	newOutDir := filepath.Join(outDirPath, fmt.Sprintf(".%s_%s", outDirName, dirSuffix))
	output = Output{
		Original: outDir,
		NewFile:  filepath.Join(newOutDir, filename),
		New:      newOutDir,
		NewTmp:   newOutDir + "_tmp",
	}
	return
}

func processOutputs(startSpecification []byte, dirSuffix string, variables map[string]string) (outputs map[string]Output, err error) {
	type JsonOutput struct {
		Output string
	}
	type JsonStart struct {
		Outputs []JsonOutput
	}

	var start JsonStart
	err = common.JsonParseBytes(startSpecification, &start)
	if err != nil {
		return
	}

	outputs = make(map[string]Output, len(start.Outputs))
	for _, o := range start.Outputs {
		if o.Output == "" {
			continue
		}

		output := processOutput(o.Output, dirSuffix, variables)
		outputs[o.Output] = output
	}

	return
}

func processOutputPaths(dirSuffix string, variables map[string]string) func(string) string {
	return func(in string) (out string) {
		output := processOutput(in, dirSuffix, variables)
		out = output.NewFile
		return
	}
}

func (self *RunningTask) prepareOutputs() (err error) {
	for _, output := range self.Outputs {
		err = os.MkdirAll(output.Original, 0o777)
		if err != nil {
			return
		}
		err = os.RemoveAll(output.NewTmp)
		if err != nil {
			return
		}
		err = os.MkdirAll(output.NewTmp, 0o777)
		if err != nil {
			return
		}
		err = os.RemoveAll(output.New)
		if err != nil {
			return
		}
		err = os.Symlink(output.NewTmp, output.New)
		if err != nil {
			return
		}

		outName := filepath.Base(output.Original)
		if strings.HasPrefix(outName, ".") {
			outName = "_" + outName
		}
		taskOutPath := filepath.Join(self.Dir, "outputs", outName)
		if _, err = os.Stat(taskOutPath); err != nil {
			if !os.IsNotExist(err) {
				return
			}
			err = os.Symlink(output.NewFile, taskOutPath)
			if err != nil {
				return
			}
		}
	}
	return
}

func (self *RunningTask) FLock() error {
	if self.flocked {
		panic("working dir already locked")
	}

	var err error

	err = os.MkdirAll(self.Dir, 0o755)
	if err != nil {
		return err
	}

	self.flock, err = flock.FlockFile(self.Dir, true, true)
	if err != nil {
		return err
	}
	self.flocked = true
	return nil
}

func (self *RunningTask) takeOutputs() error {
	var err error

	if self.takenOutputs || self.takingOutputs {
		return nil
	}

	result := make(chan error, 1)
	timeout := make(chan bool, 1)

	go func() {
		time.Sleep(1 * time.Minute) // TODO constant
		timeout <- true
	}()

	outputs := make([]Output, 0, len(self.Outputs))
	for _, output := range self.Outputs {
		outputs = append(outputs, output)
	}
	sort.Slice(outputs, func(i, j int) bool { return outputs[i].Original < outputs[j].Original })

	go func(outputs []Output) {
		self.Log(log.LOG_DEBUG, "locking outputs")

		for _, output := range outputs {
			self.Log(log.LOG_DEBUG, "locking output: %s", output.Original)

			var lock *flock.Flock
			lock, err = flock.FlockFile(output.Original, true, false)
			if err != nil {
				break
			}
			self.outputLocks[output.Original] = lock
		}
		if err != nil {
			result <- err
			return
		}
		self.Log(log.LOG_DEBUG, "outputs are now locked")
		result <- nil
	}(outputs)

	self.takingOutputs = true
	defer func() {
		self.takingOutputs = false
	}()

	self.mu.Unlock()
	select {
	case err = <-result:
		if err != nil {
			self.mu.Lock()
			return err
		}
	case <-timeout:
		self.mu.Lock()
		return errors.New("output locking timed out")
	}

	for _, output := range outputs {
		// change symlink to the public output directory
		self.Log(log.LOG_DEBUG, "relinking %s to %s", output.New, output.Original)

		err = common.AtomicSymlink(output.Original, output.New)
		if err != nil {
			self.mu.Lock()
			return err
		}

		// move already created content from the temporary directory
		self.Log(log.LOG_DEBUG, "moving files from %s to %s", output.NewTmp, output.Original)

		err = common.MoveContents(output.NewTmp, output.Original)
		if err != nil {
			self.Log(log.LOG_ERROR, "ERROR: %s", err)
		}
	}
	self.Log(log.LOG_DEBUG, "outputs linked to this task output")

	self.mu.Lock()
	self.takenOutputs = true

	return nil
}

func (self *RunningTask) leaveOutputs() error {
	var err error

	if !self.takenOutputs {
		return nil
	}

	for _, output := range self.Outputs {
		// change symlink to the output directory
		self.Log(log.LOG_DEBUG, "relinking %s to %s", output.New, output.NewTmp)

		cmd := self.command("ln", "-snf", output.NewTmp, output.New)
		_, err := cmd.CombinedOutput()
		if err != nil {
			return err
		}
	}
	self.Log(log.LOG_DEBUG, "outputs unlinked")

	self.Log(log.LOG_DEBUG, "unlocking outputs")
	for _, output := range self.Outputs {
		lock := self.outputLocks[output.Original]
		if lock != nil {
			self.Log(log.LOG_DEBUG, "unlocking output: %s", output.Original)

			err = lock.FUnlock()
			if err != nil {
				return err
			}
		}
		self.outputLocks[output.Original] = nil
	}
	self.Log(log.LOG_DEBUG, "outputs are now unlocked")
	self.takenOutputs = false

	return nil
}

func (self *RunningTask) ResetDir() error {
	var err error

	for _, output := range self.Outputs {
		err = os.RemoveAll(output.NewTmp)
		if err != nil {
			self.Log(log.LOG_ERROR, "error deleting tmp output directory: %s", err)
		}
		err = os.MkdirAll(output.NewTmp, 0o777)
		if err != nil {
			return err
		}
	}

	return nil
}
