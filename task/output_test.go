package task

import (
	"fmt"
	"path/filepath"
	"testing"

	"github.com/matryer/is"

	"git.moderntv.eu/mcloud/system/common"
)

func TestOutputs(t *testing.T) {
	t.<PERSON>llel()
	is := is.New(t)

	t.Run("empty", func(t *testing.T) {
		t.<PERSON>()
		is := is.New(t)

		var err error

		var outputs map[string]Output
		outputs, err = processOutputs([]byte("{}"), "12345", nil)
		is.NoErr(err)
		is.Equal(outputs, map[string]Output{})
	})
	t.Run("invalid", func(t *testing.T) {
		t.<PERSON>l()
		is := is.New(t)

		var err error

		_, err = processOutputs([]byte("{}}"), "12345", nil)
		is.True(err != nil)
	})
	t.Run("simple", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		var err error

		s := []byte(`{
	"outputs": [
		{
			"output": "/x/y/z/media.m3u8"
		},
		{
			"output": "/a/b/c/media.m3u8"
		}
	]
}`)

		var outputs map[string]Output
		outputs, err = processOutputs(s, "12345", nil)
		is.NoErr(err)
		expected := map[string]Output{
			"/x/y/z/media.m3u8": Output{
				Original: "/x/y/z",
				NewFile:  "/x/y/.z_12345/media.m3u8",
				New:      "/x/y/.z_12345",
				NewTmp:   "/x/y/.z_12345_tmp",
			},
			"/a/b/c/media.m3u8": Output{
				Original: "/a/b/c",
				NewFile:  "/a/b/.c_12345/media.m3u8",
				New:      "/a/b/.c_12345",
				NewTmp:   "/a/b/.c_12345_tmp",
			},
		}
		for k, _ := range expected {
			is.Equal(outputs[k], expected[k])
		}
	})
	t.Run("variables", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		var err error

		outputVariables := map[string]string{
			"%{e}": filepath.Join("/outputs", common.TaskOutputEphemeralDir),
			"%{d}": filepath.Join("/outputs", common.TaskOutputDurableDir),
		}

		s := []byte(`{
	"outputs": [
		{
			"output": "%{e}/x/y/z/media.m3u8"
		},
		{
			"output": "%{d}/a/b/c/media.m3u8"
		}
	]
}`)

		var outputs map[string]Output
		outputs, err = processOutputs(s, "12345", outputVariables)
		is.NoErr(err)
		expected := map[string]Output{
			"%{e}/x/y/z/media.m3u8": Output{
				Original: "/outputs/e/x/y/z",
				NewFile:  "/outputs/e/x/y/.z_12345/media.m3u8",
				New:      "/outputs/e/x/y/.z_12345",
				NewTmp:   "/outputs/e/x/y/.z_12345_tmp",
			},
			"%{d}/a/b/c/media.m3u8": Output{
				Original: "/outputs/d/a/b/c",
				NewFile:  "/outputs/d/a/b/.c_12345/media.m3u8",
				New:      "/outputs/d/a/b/.c_12345",
				NewTmp:   "/outputs/d/a/b/.c_12345_tmp",
			},
		}
		for k, _ := range expected {
			is.Equal(outputs[k], expected[k])
		}
	})
}

func ExampleOutputRegexp() {
	outputVariables := map[string]string{
		"%{e}": filepath.Join("/outputs", common.TaskOutputEphemeralDir),
		"%{d}": filepath.Join("/outputs", common.TaskOutputDurableDir),
	}

	s := `{
	"outputs": [
		{
			"parameters": "ffmpeg -i /dev/random '%{e}/x/y/z/media.m3u8'",
			"output": "%{e}/x/y/z/media.m3u8"
		},
		{
			"parameters": "ffmpeg -i /dev/random %{e}/x/y/z/media.m3u8",
			"output": "%{e}/x/y/z/media.m3u8"
		},
		{
			"parameters": "ffmpeg -i /dev/random '%{e}/x/y/z/media.m3u8' abcd",
			"output": "%{d}/a/b/c/media.m3u8"
		},
		{
			"parameters": "ffmpeg -i /dev/random %{e}/moderntv/stream/playboy/%v/%s-%04p-%08d.%e",
			"output": "%{e}/moderntv/stream/playboy/40/live-media.m3u8"
		}
	]
}`

	fmt.Printf("%s\n", OutputRegexp.ReplaceAllStringFunc(s, processOutputPaths("12345", outputVariables)))
	// Output:
	// {
	// 	"outputs": [
	// 		{
	// 			"parameters": "ffmpeg -i /dev/random '/outputs/e/x/y/.z_12345/media.m3u8'",
	// 			"output": "/outputs/e/x/y/.z_12345/media.m3u8"
	// 		},
	// 		{
	// 			"parameters": "ffmpeg -i /dev/random /outputs/e/x/y/.z_12345/media.m3u8",
	// 			"output": "/outputs/e/x/y/.z_12345/media.m3u8"
	// 		},
	// 		{
	// 			"parameters": "ffmpeg -i /dev/random '/outputs/e/x/y/.z_12345/media.m3u8' abcd",
	// 			"output": "/outputs/d/a/b/.c_12345/media.m3u8"
	// 		},
	// 		{
	// 			"parameters": "ffmpeg -i /dev/random /outputs/e/moderntv/stream/playboy/.%v_12345/%s-%04p-%08d.%e",
	// 			"output": "/outputs/e/moderntv/stream/playboy/.40_12345/live-media.m3u8"
	// 		}
	// 	]
	// }
}
