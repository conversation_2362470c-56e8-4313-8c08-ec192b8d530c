# Overview
Task:
* Executes processes based on its class and a specification.
* Tracking child processes in a cgroup.
* Reports state and resource usage using NATS.
* Resolves conflicts between `running` tasks with the same task ID.
* Can restart the inner process when there is no master controller.

# States
It can be in several states.
* `initializing`: the default state before doing anything.
* `starting`: the `start` script of the class is being executed.
* `running`: the `start` script is running and the outputs passed the `check` script.
* `done`: the `start` script of a finite task finished with a zero exit code.
* `failed`: the `start` script of an infinite task finished or a finite task finished with a non-zero exit code.
* `deleted`: the working directory and cgroup were removed and the entire task process is going to exit.

```mermaid
graph TD
  s(start) --> initializing

  subgraph State flow

  initializing --> starting

  starting -- check passed --> running
  starting -- crash, failed check or stop --> failed

  running -- finite task finished --> done
  running -- crash, failed check or stop --> failed

  failed -- crash with no master controller --> starting
  failed -- master controller or stop --> deleted

  done --> deleted

  end

  deleted --> e(exit)

classDef entry fill:#eeeeee,stroke-width:0px;
class s,e entry;

classDef default fill:#ccc,stroke-width:2px,stroke:#111;
```

# Outputs
Specification contains output directories.
Tasks with different task ID cannot write to the same directory.
When a task reaches the `running` state, it uses `flock` to obtain an exclusive lock for all its output directories.

Before a task is running, it is writing to a temporary directory.
However, processes write to this directory using a symlink.
This symlink is then atomically moved to the actual output directory when it is successfully locked.
This means that multiple alternatives can attempt to start on the same machine at the same time, without them affecting each other.

## Output variables
Specification has to contain variables for directories to be replaced with actual used directories.
There are currently:
* `${e}`: an "ephemeral" directory. This is for short-lived (eg. live) content that has to live on a memory-backed filesystem.
* `${d}`: a "durable" directory. For large content (eg. single-file VOD).
Note that these variables are only replaced at the beginning of individual strings (with or without quotes).


# Directory structure
* `class`: symlink to the class directory.
* `state`: current state of the task.
* `outputs`: directory containing output symlinks.
* `stderr`, `stdout`: outputs of inner processes.
* `task.log`: output of the task itself.

# Classes
* `start`: the main entry point. Exit means the entire task either failed or it's done.
* `success`: executed after reaching the `done` state.
* `failure`: executed after reaching the `failed state`.
* `check`: executed periodically. Output string is used as a warning in mCloud.
* `data`: executed periodically. Output string is provided to the `start` script on the next execution.
* `status`: executed periodically. Not used yet.

Class scripts can be either an executable (without any extension) or a Go code (with .go extension).

## Yaegi
To support classes written in Go, task uses the Yaegi library to load the class scripts at runtime.

In order for them to work on any system, task has to provide all of their non-standard dependencies.
They have to be manually added in the Makefile of this project.

# Requirements for execution
It has to be executed inside its own PID and mount namespaces.
The task exiting should stop the init process and destroy the PID namespace.
This allows to reliably get rid of any child processes.
