#!/bin/bash
set -u

SPOOL_DIR="/dev/shm/mcloud/tasks"
USER="mcloud"
GROUP="mcloud"

BASENAME_BIN="/usr/bin/basename"

show_usage() {
    echo "Usage: "$(basename "$0") "command [task]"
    echo "Omitting the task results in applying the command to all tasks"
    echo -ne "Commands:
        stop:           stop task(s)
        show-stderr:    show stderr of a specific task
        show-stdout:    show stdout of a specific task
"
    exit 2
}

is_running() {
    /sbin/start-stop-daemon --status --user "$USER"  \
                            --pidfile "$pidfile"
}

stop_pidfile() {
	/sbin/start-stop-daemon --verbose --stop --oknodo --signal "USR2" --user $USER \
                        --pidfile "$1"
    local ret=$?
	if [ $ret -eq 0 ]; then
		rm "$1" &> /dev/null
	fi

    return $ret
}

if [ $# -eq 0 ]; then
	show_usage
fi

command=$1
if [ $# -eq 1 ]; then
    if [ "$command" == "show-stderr" ] || [ "$command" == "show-stdout" ]; then
        echo "task id is mandatory for this command"
        exit 1
    fi
	exitstatus=0
	for dir in $(ls $SPOOL_DIR); do
		if [ -d "$SPOOL_DIR/$dir" ]; then
			"$0" $command "$dir" || exitstatus=1
		fi
	done
	exit $exitstatus
fi

task_id=$2

pidfile="$SPOOL_DIR/${task_id}/task.pid"
logfile="$SPOOL_DIR/${task_id}/task.log"

case "$command" in
    stop)
        stop_pidfile "$pidfile"

        exit $?
    ;;
    status)
		if ! is_running; then
            echo "Task $task_id is NOT running"
            exit 1
        fi
        state=$(cat "$SPOOL_DIR/${task_id}/state")
        if [ "$state" != "running" ]; then #TODO playlist/segment download test
            echo "Task $task_id is running [$(cat $pidfile)] but in the state '$state'"
            exit 2
        fi

        echo "Task $task_id is running [$(cat $pidfile)] ok"
    ;;
    show-stdout)
        tail -f "$SPOOL_DIR/${task_id}/stdout"
    ;;
    show-stderr)
        tail -f "$SPOOL_DIR/${task_id}/stderr"
    ;;
    *)
        show_usage
    ;;
esac
