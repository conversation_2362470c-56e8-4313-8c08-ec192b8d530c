package controller

import (
	"time"

	"github.com/prometheus/client_golang/prometheus"
)

type Metrics struct {
	ReceivedNatsMessages *prometheus.HistogramVec
	PendingNatsMessages  *pendingMetric
	TaskStatePhases      *prometheus.CounterVec
}

const MetricsNamespace = "mcloud_controller"

func NewMetrics() *Metrics {
	return &Metrics{
		ReceivedNatsMessages: prometheus.NewHistogramVec(prometheus.HistogramOpts{
			Namespace: MetricsNamespace,
			Name:      "nats_received_us",
			Help:      "Duration in microseconds of processing of NATS messages by type",
			Buckets:   []float64{50, 2e2, 5e2, 2e3, 5e3, 2e4, 2e5, 5e5, 2e6, 5e6}, // in microseconds
		}, []string{"type"}),
		PendingNatsMessages: &pendingMetric{
			msgsDesc: prometheus.NewDesc(
				MetricsNamespace+"_nats_pending_messages",
				"Current number of messages pending for subscription",
				[]string{"type"},
				nil,
			),
			bytesDesc: prometheus.NewDesc(
				MetricsNamespace+"_nats_pending_bytes",
				"Current number of message bytes pending for subscription",
				[]string{"type"},
				nil,
			),
		},
		TaskStatePhases: prometheus.NewCounterVec(prometheus.CounterOpts{
			Namespace: MetricsNamespace,
			Name:      "task_state_phases",
			Help:      "Number of times a task_state phase has been reached",
		}, []string{"phase"}),
	}
}

func (metrics *Metrics) MustRegister(registry prometheus.Registerer) {
	registry.MustRegister(metrics.ReceivedNatsMessages)
	registry.MustRegister(metrics.PendingNatsMessages)
	registry.MustRegister(metrics.TaskStatePhases)
}

func (metrics *Metrics) AddReceiveNatsMessage(typ string, start time.Time) {
	processingDuration := time.Since(start)
	metrics.ReceivedNatsMessages.WithLabelValues(typ).Observe(float64(processingDuration.Microseconds()))
}

func (metrics *Metrics) AddNatsSubscriptionPending(typ string, pendingFn func() (msgs int, bytes int, err error)) {
	metrics.PendingNatsMessages.AddSubscription(typ, pendingFn)
}

func (metrics *Metrics) IncTaskState(phase string) {
	metrics.TaskStatePhases.WithLabelValues(phase).Inc()
}

type pendingMetric struct {
	msgsDesc   *prometheus.Desc
	bytesDesc  *prometheus.Desc
	types      []string
	pendingFns []func() (msgs int, bytes int, err error)
}

func (metric *pendingMetric) AddSubscription(typ string, pendingFn func() (msgs int, bytes int, err error)) {
	metric.types = append(metric.types, typ)
	metric.pendingFns = append(metric.pendingFns, pendingFn)
}

func (metric *pendingMetric) Describe(ch chan<- *prometheus.Desc) {
	ch <- metric.msgsDesc
	ch <- metric.bytesDesc
}

func (metric *pendingMetric) Collect(ch chan<- prometheus.Metric) {
	for i, typ := range metric.types {
		pendingFn := metric.pendingFns[i]
		msgs, bytes, err := pendingFn()
		if err != nil {
			continue
		}

		m, err := prometheus.NewConstMetric(
			metric.msgsDesc,
			prometheus.GaugeValue,
			float64(msgs),
			typ,
		)
		if err == nil {
			ch <- m
		}

		m, err = prometheus.NewConstMetric(
			metric.bytesDesc,
			prometheus.GaugeValue,
			float64(bytes),
			typ,
		)
		if err == nil {
			ch <- m
		}
	}
}
