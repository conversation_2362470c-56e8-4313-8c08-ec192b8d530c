package db

import (
	"database/sql"
	"errors"
	"sync"
	"time"

	"github.com/go-sql-driver/mysql"

	gocommon "git.moderntv.eu/go/common"
	"git.moderntv.eu/mcloud/system/common"
)

var (
	ErrorNotConnected = errors.New("database not connected")
)

type DB struct {
	checkStmt *sql.Stmt

	insertControllerStmt *sql.Stmt
	cleanControllerStmt  *sql.Stmt //TODO add delete?

	insertOriginNodeStmt  *sql.Stmt
	cleanOriginNodeStmt   *sql.Stmt
	selectOriginNodesStmt *sql.Stmt

	selectNodeTasksStmt *sql.Stmt
	insertNodeTaskStmt  *sql.Stmt
	deleteNodeTaskStmt  *sql.Stmt

	deleteTaskStmt    *sql.Stmt
	setTaskActionStmt *sql.Stmt
	storeTaskDataStmt *sql.Stmt
	selectTasksStmt   *sql.Stmt

	enabled        bool
	connected      bool
	db             *sql.DB
	connectionBump chan struct{}
	reconnect      chan struct{}
	sync.RWMutex
	*sync.Cond
}

func NewDB(url string, logger mysql.Logger) *DB {
	err := mysql.SetLogger(logger)
	if err != nil {
		gocommon.Log("ERROR setting logger: %s", err)
	}

	self := DB{
		connectionBump: make(chan struct{}, 1),
		reconnect:      make(chan struct{}, 1),
	}
	self.Cond = sync.NewCond(self.RLocker())

	go self.ConnectionHandler(url)

	return &self
}

func (dbObj *DB) Enabled() bool {
	dbObj.RLock()
	defer dbObj.RUnlock()

	return dbObj.enabled
}
func (dbObj *DB) Connect() {
	dbObj.Lock()
	if !dbObj.enabled {
		dbObj.enabled = true
		dbObj.Cond.Broadcast()
	}
	dbObj.Unlock()

}
func (dbObj *DB) Disconnect() {
	dbObj.Lock()
	if dbObj.enabled {
		dbObj.enabled = false
		dbObj.Cond.Broadcast()
	}
	dbObj.Unlock()
	select {
	case dbObj.connectionBump <- struct{}{}:
	default:
	}
}
func (dbObj *DB) Reconnect() {
	select {
	case dbObj.reconnect <- struct{}{}:
	default:
	}
}

func (dbObj *DB) TryConnect(l string) error {
	var err error

	var db *sql.DB
	db, err = sql.Open("mysql", l)
	if err != nil {
		return nil
	}
	defer db.Close()
	//db.SetMaxOpenConns(32)
	db.SetMaxIdleConns(16)
	db.SetConnMaxLifetime(15 * time.Minute)

	var checkStmt *sql.Stmt
	checkStmt, err = db.Prepare("SELECT 1")
	if err != nil {
		return err
	}
	defer checkStmt.Close()

	var insertControllerStmt *sql.Stmt
	insertControllerStmt, err = db.Prepare("INSERT INTO Controllers (id, type, host, state, priority, hasDbConnection, lastOnlineTime, lastMasterTime) VALUES (?, ?, ?, ?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE host=VALUES(host),state=VALUES(state),priority=VALUES(priority),hasDbConnection=VALUES(hasDbConnection),lastOnlineTime=VALUES(lastOnlineTime),lastMasterTime=COALESCE(VALUES(lastMasterTime),lastMasterTime)")
	if err != nil {
		return err
	}
	defer insertControllerStmt.Close()

	var cleanControllerStmt *sql.Stmt
	cleanControllerStmt, err = db.Prepare("INSERT INTO Controllers (id, state, hasDbConnection) VALUES (?, DEFAULT, DEFAULT) ON DUPLICATE KEY UPDATE state=VALUES(state),hasDbConnection=VALUES(hasDbConnection)")
	if err != nil {
		return err
	}
	defer cleanControllerStmt.Close()

	var insertOriginNodeStmt *sql.Stmt
	insertOriginNodeStmt, err = db.Prepare("INSERT INTO OriginNodes (id, host, state, resourceAvailable, resourceFree, resourceTotal, lastOnlineTime) VALUES (?, ?, ?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE host=VALUES(host),state=VALUES(state),resourceAvailable=VALUES(resourceAvailable),resourceFree=VALUES(resourceFree),resourceTotal=VALUES(resourceTotal),lastOnlineTime=VALUES(lastOnlineTime)")
	if err != nil {
		return err
	}
	defer insertOriginNodeStmt.Close()

	var cleanOriginNodeStmt *sql.Stmt
	cleanOriginNodeStmt, err = db.Prepare("INSERT INTO OriginNodes (id, state, resourceAvailable, resourceFree, resourceTotal) VALUES (?, DEFAULT, DEFAULT, DEFAULT, DEFAULT) ON DUPLICATE KEY UPDATE state=VALUES(state),resourceAvailable=VALUES(resourceAvailable),resourceFree=VALUES(resourceFree),resourceTotal=VALUES(resourceTotal)")
	if err != nil {
		return err
	}
	defer cleanOriginNodeStmt.Close()

	var selectOriginNodesStmt *sql.Stmt
	selectOriginNodesStmt, err = db.Prepare("SELECT id, capabilities, state, enabled FROM OriginNodes")
	if err != nil {
		return err
	}
	defer selectOriginNodesStmt.Close()

	var selectNodeTasksStmt *sql.Stmt
	selectNodeTasksStmt, err = db.Prepare("SELECT task, originNode, revision, lastOnlineTime, startTime FROM NodeTasks")
	if err != nil {
		return err
	}
	defer selectNodeTasksStmt.Close()

	var insertNodeTaskStmt *sql.Stmt
	insertNodeTaskStmt, err = db.Prepare("INSERT INTO NodeTasks (task, originNode, revision, state, status, warnings, lastRunningTime, lastOnlineTime, startTime, selectedResources, resourceUsage) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE revision=VALUES(revision),state=VALUES(state),status=VALUES(status),warnings=VALUES(warnings),lastRunningTime=COALESCE(VALUES(lastRunningTime),lastRunningTime),lastOnlineTime=VALUES(lastOnlineTime),startTime=VALUES(startTime),selectedResources=VALUES(selectedResources),resourceUsage=VALUES(resourceUsage)")
	if err != nil {
		return err
	}
	defer insertNodeTaskStmt.Close()

	var deleteNodeTaskStmt *sql.Stmt
	deleteNodeTaskStmt, err = db.Prepare("DELETE FROM NodeTasks WHERE `task`=? AND `originNode`=? AND `revision`=?")
	if err != nil {
		return err
	}
	defer deleteNodeTaskStmt.Close()

	/*selectTaskStmt, err = db.Prepare("SELECT id FROM Tasks WHERE taskId=?")
	if err != nil {
		return err
	}
	defer selectTaskStmt.Close()*/

	var deleteTaskStmt *sql.Stmt
	deleteTaskStmt, err = db.Prepare("DELETE FROM Tasks WHERE id = ?")
	if err != nil {
		return err
	}
	defer deleteTaskStmt.Close()

	var setTaskActionStmt *sql.Stmt
	setTaskActionStmt, err = db.Prepare("UPDATE Tasks SET action = ? WHERE id = ?")
	if err != nil {
		return err
	}
	defer setTaskActionStmt.Close()

	var storeTaskDataStmt *sql.Stmt
	storeTaskDataStmt, err = db.Prepare("INSERT INTO TaskData (taskId, privateData) VALUES (?, ?) ON DUPLICATE KEY UPDATE privateData=COALESCE(VALUES(privateData),privateData)")
	if err != nil {
		return err
	}
	defer storeTaskDataStmt.Close()

	var selectTasksStmt *sql.Stmt
	selectTasksStmt, err = db.Prepare("SELECT id, Tasks.taskId, alternative, `order`, level, resource, resourceCode, currentRevision, class, finite, action, priority, parameters, creationTime, requirements, requiredResources, comment, dependsOn, after, requiresFinishedDeps, TaskData.privateData FROM Tasks LEFT JOIN TaskData ON Tasks.taskId = TaskData.taskId ORDER BY priority DESC, `order` ASC")
	if err != nil {
		return err
	}
	defer selectTasksStmt.Close()

	ticker := time.NewTicker(30 * time.Second) //TODO constant
	defer ticker.Stop()

	dbObj.Lock()
	dbObj.db = db
	dbObj.checkStmt = checkStmt
	dbObj.insertControllerStmt = insertControllerStmt
	dbObj.cleanControllerStmt = cleanControllerStmt
	dbObj.insertOriginNodeStmt = insertOriginNodeStmt
	dbObj.cleanOriginNodeStmt = cleanOriginNodeStmt
	dbObj.selectOriginNodesStmt = selectOriginNodesStmt
	dbObj.selectNodeTasksStmt = selectNodeTasksStmt
	dbObj.insertNodeTaskStmt = insertNodeTaskStmt
	dbObj.deleteNodeTaskStmt = deleteNodeTaskStmt
	dbObj.deleteTaskStmt = deleteTaskStmt
	dbObj.setTaskActionStmt = setTaskActionStmt
	dbObj.storeTaskDataStmt = storeTaskDataStmt
	dbObj.selectTasksStmt = selectTasksStmt
	dbObj.Unlock()

	var reconnect bool
	var enabled bool
	for {
		dbObj.RLock()
		enabled = dbObj.enabled
		dbObj.RUnlock()

		conns := db.Stats().OpenConnections
		if conns > 50 {
			gocommon.Log("WARNING: %d open mysql connections", conns)
		}

		if reconnect {
			err = errors.New("reconnecting to mysql server")
			return err
		}
		if !enabled {
			err = errors.New("disconnecting from mysql server")
			return err
		}

		// check db connection
		err = db.Ping()
		if err != nil {
			return err
		}
		err = dbObj.checkConnection()
		if err != nil {
			return err
		}

		dbObj.Lock()
		if !dbObj.connected {
			dbObj.connected = true
			gocommon.Log("mysql: connection established") //TODO remove
		}
		dbObj.Broadcast()
		dbObj.Unlock()

		reconnect = false
		select {
		case <-dbObj.connectionBump:
		case <-dbObj.reconnect:
			reconnect = true
		case <-ticker.C:
		}
	}
}

func (dbObj *DB) cleanStmts() {
	dbObj.checkStmt = nil
	dbObj.insertControllerStmt = nil
	dbObj.cleanControllerStmt = nil
	dbObj.insertOriginNodeStmt = nil
	dbObj.cleanOriginNodeStmt = nil
	dbObj.selectOriginNodesStmt = nil
	dbObj.selectNodeTasksStmt = nil
	dbObj.insertNodeTaskStmt = nil
	dbObj.deleteNodeTaskStmt = nil
	dbObj.deleteTaskStmt = nil
	dbObj.setTaskActionStmt = nil
	dbObj.storeTaskDataStmt = nil
	dbObj.selectTasksStmt = nil
}

func (dbObj *DB) ConnectionHandler(url string) {
	var err error

	ticker := time.NewTicker(5 * time.Second) //TODO constant
	defer ticker.Stop()
	for {
		dbObj.RLock()
		for !dbObj.enabled {
			dbObj.Wait()
		}
		dbObj.RUnlock()

		err = dbObj.TryConnect(url)
		if err != nil {
			gocommon.Log("ERROR in mysql connection: %s", err) //TODO remove
		}
		dbObj.Lock()
		dbObj.connected = false
		dbObj.cleanStmts()
		dbObj.Unlock()
		dbObj.Broadcast()

		<-ticker.C
	}
}

func (dbObj *DB) WaitForConnection() error {
	dbObj.RLock()
	defer dbObj.RUnlock()

	return dbObj.waitForConnection()
}

func (dbObj *DB) waitForConnection() error {
	for {
		if !dbObj.enabled {
			return errors.New("db connection not enabled")
		}

		if dbObj.connected {
			break
		}

		dbObj.Wait()
	}

	return nil
}

func (dbObj *DB) Connected() bool {
	dbObj.RLock()
	defer dbObj.RUnlock()

	return dbObj.connected
}

func (dbObj *DB) Begin() (tx *sql.Tx, err error) {
	dbObj.RLock()
	if !dbObj.connected {
		dbObj.RUnlock()

		err = ErrorNotConnected
		return
	}
	tx, err = dbObj.db.Begin()
	dbObj.RUnlock()

	return
}

/*func (self *DB) SelectNodeTasks() (*sql.Rows, error) {
	var err error
	err = self.WaitForConnection()
	if err != nil {
		return nil, err
	}

	return self.selectNodeTasksStmt.Query()
}*/

func (dbObj *DB) checkConnection() error {
	dbObj.RLock()
	if dbObj.checkStmt == nil {
		dbObj.RUnlock()
		return errors.New("db not initialized")
	}
	stmt := dbObj.checkStmt
	dbObj.RUnlock()

	rows, err := stmt.Query()
	if err != nil {
		return err
	}
	defer rows.Close()
	for rows.Next() {
	}
	err = rows.Err()
	if err != nil {
		return err
	}

	return nil
}

func (dbObj *DB) SelectNodeTasks() (*sql.Rows, error) {
	dbObj.RLock()
	if !dbObj.connected {
		dbObj.RUnlock()
		return nil, ErrorNotConnected
	}
	stmt := dbObj.selectNodeTasksStmt
	dbObj.RUnlock()

	rows, err := stmt.Query()
	return rows, err
}

func (dbObj *DB) InsertNodeTask(tx *sql.Tx, nodeTask *common.DbNodeTask) error {
	dbObj.RLock()
	if !dbObj.connected {
		dbObj.RUnlock()
		return ErrorNotConnected
	}
	stmt := dbObj.insertNodeTaskStmt
	dbObj.RUnlock()

	if tx != nil {
		stmt = tx.Stmt(stmt)
		defer stmt.Close()
	}

	_, err := stmt.Exec(nodeTask.Task, nodeTask.Node, nodeTask.Revision, nodeTask.State, nodeTask.Status, nodeTask.Warnings, nodeTask.LastRunningTime, nodeTask.LastOnlineTime, nodeTask.StartTime, nodeTask.SelectedResources.Json(), nodeTask.ResourceUsage.Json())
	return err
}

func (dbObj *DB) DeleteNodeTask(nodeTask *common.DbNodeTask) error {
	dbObj.RLock()
	if !dbObj.connected {
		dbObj.RUnlock()
		return ErrorNotConnected
	}
	stmt := dbObj.deleteNodeTaskStmt
	dbObj.RUnlock()

	_, err := stmt.Exec(nodeTask.Task, nodeTask.Node, nodeTask.Revision)
	return err
}

func (dbObj *DB) InsertNode(tx *sql.Tx, node *common.DbNode) error {
	dbObj.RLock()
	if !dbObj.connected {
		dbObj.RUnlock()
		return ErrorNotConnected
	}
	stmt := dbObj.insertOriginNodeStmt
	dbObj.RUnlock()

	if tx != nil {
		stmt = tx.Stmt(stmt)
		defer stmt.Close()
	}
	_, err := stmt.Exec(node.Id, node.Host, node.State, node.AvailableResources.Json(), node.FreeResources.Json(), node.TotalResources.Json(), node.LastOnline)
	return err
}

func (dbObj *DB) SelectNodes() (*sql.Rows, error) {
	dbObj.RLock()
	if !dbObj.connected {
		dbObj.RUnlock()
		return nil, ErrorNotConnected
	}
	stmt := dbObj.selectOriginNodesStmt
	dbObj.RUnlock()

	return stmt.Query()
}

func (dbObj *DB) CleanNode(node *common.DbNode) error {
	dbObj.RLock()
	if !dbObj.connected {
		dbObj.RUnlock()
		return ErrorNotConnected
	}
	stmt := dbObj.cleanOriginNodeStmt
	dbObj.RUnlock()

	_, err := stmt.Exec(node.Id)
	return err
}

func (dbObj *DB) SelectTasks() (*sql.Rows, error) {
	dbObj.RLock()
	if !dbObj.connected {
		dbObj.RUnlock()
		return nil, ErrorNotConnected
	}
	stmt := dbObj.selectTasksStmt
	dbObj.RUnlock()

	return stmt.Query()
}

func (dbObj *DB) DeleteTask(task *common.DbTask) error {
	dbObj.RLock()
	if !dbObj.connected {
		dbObj.RUnlock()
		return ErrorNotConnected
	}
	stmt := dbObj.deleteTaskStmt
	dbObj.RUnlock()

	_, err := stmt.Exec(task.DbId)
	return err
}

func (dbObj *DB) DoneTask(task *common.DbTask) error {
	dbObj.RLock()
	if !dbObj.connected {
		dbObj.RUnlock()
		return ErrorNotConnected
	}
	stmt := dbObj.setTaskActionStmt
	dbObj.RUnlock()

	_, err := stmt.Exec("wait", task.DbId)
	return err
}

func (dbObj *DB) StoreTaskData(tx *sql.Tx, task *common.DbTask, data string) error {
	dbObj.RLock()
	if !dbObj.connected {
		dbObj.RUnlock()
		return ErrorNotConnected
	}
	stmt := dbObj.storeTaskDataStmt
	dbObj.RUnlock()

	if tx != nil {
		stmt = tx.Stmt(stmt)
		defer stmt.Close()
	}

	_, err := stmt.Exec(task.Id, data)
	return err
}

func (dbObj *DB) InsertController(tx *sql.Tx, controller common.DbController) error {
	dbObj.RLock()
	if !dbObj.connected {
		dbObj.RUnlock()
		return ErrorNotConnected
	}
	stmt := dbObj.insertControllerStmt
	dbObj.RUnlock()

	if tx != nil {
		stmt = tx.Stmt(stmt)
		defer stmt.Close()
	}

	_, err := stmt.Exec(controller.Id, controller.Type, controller.Host, controller.State_, controller.Priority, controller.HasDBConnection, controller.LastOnline, controller.LastMaster)
	return err
}

func (dbObj *DB) CleanController(controllerId string) error {
	dbObj.RLock()
	if !dbObj.connected {
		dbObj.RUnlock()
		return ErrorNotConnected
	}
	stmt := dbObj.cleanControllerStmt
	dbObj.RUnlock()

	_, err := stmt.Exec(controllerId)
	return err
}

func (dbObj *DB) DB() *sql.DB {
	return dbObj.db
}
