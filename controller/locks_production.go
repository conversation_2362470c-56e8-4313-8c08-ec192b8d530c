//go:build production
// +build production

package controller

type LockState struct{}

var (
	LockUnlocked = LockState{}
	LockLocked   = LockState{}
	LockRLocked  = LockState{}
)

func LockLock(current *LockState) {
}

func LockRLock(current *LockState) {
}

func LockUnlock(current *LockState) {
}

func LockRUnlock(current *LockState) {
}

func AssertLock(current LockState, required LockState) {
}

type Locks struct {
	Tasks      LockState
	Nodes      LockState
	Controller LockState
	Task       LockState
	Node       LockState
	NodeTask   LockState
}

func NewLocks() Locks {
	return Locks{}
}

///

func (l Locks) AssertUnlocked() {
}

///

func (l Locks) LockNodes() {
}

func (l Locks) UnlockNodes() {
}

func (l Locks) RLockNodes() {
}

func (l Locks) RUnlockNodes() {
}

///

func (l Locks) LockNode() {
}

func (l Locks) UnlockNode() {
}

func (l Locks) RLockNode() {
}

func (l Locks) RUnlockNode() {
}

///

func (l Locks) LockTask() {
}

func (l Locks) UnlockTask() {
}

func (l Locks) RLockTask() {
}

func (l Locks) RUnlockTask() {
}

///

func (l Locks) LockTasks() {
}

func (l Locks) UnlockTasks() {
}

func (l Locks) RLockTasks() {
}

func (l Locks) RUnlockTasks() {
}

///

func (l Locks) LockNodeTask() {
}

func (l Locks) UnlockNodeTask() {
}

func (l Locks) RLockNodeTask() {
}

func (l Locks) RUnlockNodeTask() {
}

///

func (l Locks) LockController() {
}

func (l Locks) UnlockController() {
}

func (l Locks) RLockController() {
}

func (l Locks) RUnlockController() {
}
