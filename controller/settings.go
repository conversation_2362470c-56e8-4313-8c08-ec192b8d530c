package controller

import (
	"time"
)

const (
	TaskListOrderHorizontal = 1 << iota
	TaskListOrderVertical
	TaskListOrderAutomatic
)

const (
	ReprioritizeDuration = 1 * time.Minute

	// should be less than NodeTaskRemoveTimeout
	QuorumCheckInterval = 10 * time.Second
)

// timeouts
const (
	ControllerRemoveTimeout    = 7 * time.Second
	NodeRemoveTimeout          = 10 * time.Second
	NodeTaskRemoveTimeout      = 60 * time.Second
	DummyNodeTaskRemoveTimeout = 5 * time.Second
)

// planning
const (
	PreferNodesWithChildren = true
	TaskRandomStartDelayMax = 1 * time.Second
	DefaultTaskListOrder    = TaskListOrderAutomatic
	RunLimitEnable          = true
	RunLimitRate            = 60 // number of seconds to get a new attempt "token"
	RunLimitBurst           = 10 // burst number of attempts
	FiniteTaskAttempts      = 0
)

// DB
const (
	DatabaseUpdateTime   = 10 * time.Minute
	SelfControllerInsert = true
)
