# States
* `idle`: passive listening and updating of internal data.
* `master`: pushing data into the database and executing tasks. Only one such instance should be running at any given time.
* `error`: communication/database error. This instance is not going to attempt getting into the `master` state.

# Master selection
Controller in the `idle` states switches to the `master` state if there is no other controller in the `master` state.
If multiple controllers attempt to do this at the same time, they decide based on several criteria which one will drop their `master` state (eg. priority, working database connections...).

# Database
Only the master controller writes to the database.
Entries are not deleted by controller unless stated otherwise.

## Controllers
Detected controllers are stored in the Controllers table.

## Tasks and node tasks
Controller reads tasks from the `Tasks` table.
Tasks running on a specific node are called node tasks and controller stored their state in the `NodeTasks` table.

Additionally there is a `TaskData` table for storing arbitrary Task data (output of `data` script of their class).
This data is then provided to newly started tasks to have the ability to transfer state between individual executions (eg. HLS sequence numbers).

Entries in `NodeTasks` are deleted after the node tasks reaches a `deleted` state or it stops sending status updates.
The only tasks that are deleted from `Tasks` are either completely finished finite tasks or not running tasks set to `terminate`.

## Nodes
Nodes are stored in the `OriginNodes` table.

# Resources
Resources allow to select the best node for a task.
This means that the target node has certain capabilities (like having a GPU) and has the most available resources.

Resources are identified by a string key and the value is an array of values.
Each index in the array represents a device providing this resource.
Devices have the same index on all resources they provide.

Resources are computed in 3 variants.
Free, reserved and available.
The free value represents the physical unused part on the node.
The reserve can be added either by the node or by the controller (most cases).
Available resource is the free value minus the final reserved value.
Reservations in controller are composed of the following values:
* The reserve of all starting tasks.
* Maximum of all reserves.

Where the "reserve" if a task is the difference between the theoretical requirement and the actual usage.

# Node selection
There are two ways to select nodes in this order:
1) Nodes (or alternatively group) where there are children or parents running (unless it creates resource imbalance).
1) All nodes sorted based on a resource selected to be the most scarce (default `CPU`). If a resource has more values, their maximum is used for the ordering.

Each node is checked for capabilities and available resources.
When a node is selected, the task gets reserved all of its required resources on the node.

# Alternative task planning
When a task ID has multiple alternatives, they are executed all according to their order.
The first one to get to the `running` state wins and stays running.
There is a delay between each execution to allow the preferred tasks to start first.

# Finite tasks processing
Finite tasks behave differently from infinite ones.
A done/failed finite task waits (while re-sending its state) until a master controller confirms receiving and processing the data.
This is to make sure the result data is received and processed and also to avoid unnecessary re-starts.

1) The result of the node task is sent to the mCloud API.
1) If the node task finished with a `done` state, its task's `action` is set to `wait` to prevent being started again.
1) A confirmation message is sent to the node task.
1) Done tasks with action set to `wait` are removed from the database on the next DB update.
