package nodetask

import (
	"sync"
	"sync/atomic"
	"time"

	"git.moderntv.eu/mcloud/system/common"
	"git.moderntv.eu/mcloud/system/controller"
)

type NodeTask struct {
	dbNodeTask *atomic.Value

	mu     sync.RWMutex
	remove *time.Timer
}

func NewNodeTask(dbNodeTask common.DbNodeTask) *NodeTask {
	nodeTask := &NodeTask{
		dbNodeTask: &atomic.Value{},
	}
	nodeTask.dbNodeTask.Store(&dbNodeTask)

	return nodeTask
}

func (nodeTask *NodeTask) DbNodeTask() *common.DbNodeTask {
	return nodeTask.dbNodeTask.Load().(*common.DbNodeTask)
}

func (nodeTask *NodeTask) SetDbNodeTask(dbNodeTask common.DbNodeTask, removeTimeout time.Duration, locks controller.Locks) (oldDbNodeTask *common.DbNodeTask) {
	locks.LockNodeTask()
	nodeTask.mu.Lock()
	controller.AssertLock(locks.NodeTask, controller.LockLocked)
	if nodeTask.remove != nil {
		nodeTask.remove.Reset(removeTimeout)
	}
	oldDbNodeTask = nodeTask.dbNodeTask.Swap(&dbNodeTask).(*common.DbNodeTask)

	locks.UnlockNodeTask()
	nodeTask.mu.Unlock()
	return
}

func (nodeTask *NodeTask) Remove(locks controller.Locks) {
	nodeTask.mu.Lock()
	if nodeTask.remove != nil {
		controller.AssertLock(locks.Task, controller.LockLocked)
		nodeTask.remove.Stop()
		nodeTask.remove = nil
	}
	nodeTask.mu.Unlock()
}

func (nodeTask *NodeTask) SetRemoveTimer(remove *time.Timer, locks controller.Locks) {
	locks.LockNodeTask()
	nodeTask.mu.Lock()
	defer nodeTask.mu.Unlock()
	defer locks.UnlockNodeTask()

	if nodeTask.remove != nil {
		nodeTask.remove.Stop()
	}
	nodeTask.remove = remove
}
