package task

import (
	"sort"

	"git.moderntv.eu/mcloud/system/controller"
)

// TaskList represents a list of Task pointers that can be sorted.
// TaskList does not locking of itself, the caller is responsible for locking.
type TaskList struct {
	Order uint8
	tasks []*Task
}

func (taskList *TaskList) Len() int {
	return len(taskList.tasks)
}

func (taskList *TaskList) Less(a int, b int) bool {
	return taskList.less(taskList.tasks[a], taskList.tasks[b])
}

func (taskList *TaskList) less(taskA *Task, taskB *Task) bool {
	var dbTaskA = taskA.DbTask()
	var dbTaskB = taskB.DbTask()

	prioA := 0
	if dbTaskA.Priority != nil {
		prioA = *dbTaskA.Priority
	}
	prioB := 0
	if dbTaskB.Priority != nil {
		prioB = *dbTaskB.Priority
	}

	var order = taskList.Order
	if order == controller.TaskListOrderAutomatic {
		if taskA.AAHP() && taskB.AAHP() {
			order = controller.TaskListOrderVertical
		} else {
			order = controller.TaskListOrderHorizontal
		}
	}

	switch order {
	case controller.TaskListOrderHorizontal:
		if prioA > prioB {
			return true
		}
		if prioA == prioB && dbTaskA.DbId < dbTaskB.DbId {
			return true
		}
		return false
	case controller.TaskListOrderVertical:
		if dbTaskA.Order < dbTaskB.Order {
			return true
		}
		if dbTaskA.Order == dbTaskB.Order && prioA > prioB {
			return true
		}
		if dbTaskA.Order == dbTaskB.Order && prioA == prioB && dbTaskA.DbId < dbTaskB.DbId {
			return true
		}
		return false
	default:
		panic("bad TaskList order")
	}
}

func (taskList *TaskList) Swap(i int, j int) {
	taskList.tasks[i], taskList.tasks[j] = taskList.tasks[j], taskList.tasks[i]
	taskList.tasks[i].listIndex = i
	taskList.tasks[j].listIndex = j
}

func (taskList *TaskList) Add(task *Task) {
	if task.listIndex != -1 {
		return
	}

	taskList.tasks = append(taskList.tasks, nil)
	i := sort.Search(len(taskList.tasks), func(i int) bool {
		if taskList.tasks[i] == nil {
			return true
		}
		return taskList.less(task, taskList.tasks[i])
	})
	copy(taskList.tasks[i+1:], taskList.tasks[i:])
	for _, t := range taskList.tasks[i+1:] {
		t.listIndex += 1
	}
	taskList.tasks[i] = task
	task.listIndex = i
}

func (taskList *TaskList) Remove(task *Task) {
	index := task.listIndex
	if index == -1 {
		return
	}

	taskList.tasks = append(taskList.tasks[:index], taskList.tasks[index+1:]...)
	for _, afterTask := range taskList.tasks[index:] {
		afterTask.listIndex -= 1
	}
	task.listIndex = -1

	/*	newSelf := append(self[:index], self[index+1:]...)

		return newSelf, -1*/
}

// List returns a copy of the list of tasks.
func (taskList *TaskList) List() (list []*Task) {
	list = make([]*Task, len(taskList.tasks))
	copy(list, taskList.tasks)
	return
}
