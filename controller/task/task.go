package task

import (
	"sync"
	"sync/atomic"
	"time"

	"git.moderntv.eu/mcloud/log"
	"git.moderntv.eu/mcloud/system/common"
	"git.moderntv.eu/mcloud/system/controller"
	"git.moderntv.eu/mcloud/system/controller/nodetask"
	"github.com/rkollar/ratelimit"
)

type Task struct {
	dbTask atomic.Value

	mu                        sync.RWMutex
	privateData               *string
	listIndex                 int
	nodeTasks                 map[string]*nodetask.NodeTask
	children                  map[int]*Task
	zombie                    bool
	allAlternativesHaveParent atomic.Bool
	runLimit                  *ratelimit.Bucket
	StartedCounter            atomic.Int64
}

func NewTask(zombie bool) *Task {
	task := &Task{
		dbTask:    atomic.Value{},
		listIndex: -1,
		nodeTasks: make(map[string]*nodetask.NodeTask),
		children:  make(map[int]*Task),
		runLimit:  ratelimit.NewBucket(runLimitUnit/controller.RunLimitRate, runLimitUnit*controller.RunLimitBurst),
		zombie:    zombie,
	}
	task.allAlternativesHaveParent.Store(false)

	return task
}

const runLimitUnit = 1000000

func (task *Task) AllowRunAttempt(onlyCheck bool, locks controller.Locks) bool {
	locks.LockTask()
	task.mu.Lock()
	defer task.mu.Unlock()
	defer locks.UnlockTask()

	if onlyCheck {
		controller.AssertLock(locks.Task, controller.LockRLocked)
		return task.runLimit.Check(runLimitUnit)
	}
	controller.AssertLock(locks.Task, controller.LockLocked)
	return task.runLimit.Use(runLimitUnit)
}

func (task *Task) GetRunning(flags uint8, locks controller.Locks) (running bool, nodeTaskCount int, zombie bool) {
	locks.RLockTask()
	task.mu.RLock()
	defer task.mu.RUnlock()
	defer locks.RUnlockTask()

	return task.isRunning(flags, locks), len(task.nodeTasks), task.zombie
}

func (task *Task) isRunning(flags uint8, locks controller.Locks) bool {
	controller.AssertLock(locks.Task, controller.LockRLocked)
	for _, nodeTask := range task.nodeTasks {
		dbNodeTask := nodeTask.DbNodeTask()

		if dbNodeTask.State == "dummy" {
			continue
		}
		if flags&controller.CountAnyRevision == 0 && dbNodeTask.Revision < task.DbTask().Revision {
			continue
		}

		if (flags&controller.CountAnyState > 0 && dbNodeTask.State != "deleted") || dbNodeTask.State == "running" || dbNodeTask.State == "done" {
			return true
		}
		if flags&controller.CountNew > 0 && (dbNodeTask.State == "dummy" || dbNodeTask.State == "initializing" || (dbNodeTask.State == "starting" && time.Since(dbNodeTask.CreationTime).Seconds() < 60)) { // TODO constant
			return true
		}
	}

	return false
}

func (task *Task) Runnable() bool {
	dbTask := task.DbTask()

	if dbTask.Action != "run" {
		return false
	}

	if controller.FiniteTaskAttempts > 0 {
		sc := task.StartedCounter.Load()
		if dbTask.Finite && sc >= controller.FiniteTaskAttempts {
			return false
		}
	}

	return true
}

// DbTask does not require locking
func (task *Task) DbTask() *common.DbTask {
	dbTask := task.dbTask.Load()
	if dbTask == nil {
		return nil
	}
	return dbTask.(*common.DbTask)
}

// SetDbTask does not require locking
func (task *Task) SetDbTask(dbTask common.DbTask) {
	var newRevision bool
	oldDbTask := task.DbTask()
	if oldDbTask == nil || oldDbTask != nil && dbTask.Revision > oldDbTask.Revision {
		newRevision = true
	}

	if newRevision {
		task.StartedCounter.Store(0)
	}

	task.dbTask.Store(&dbTask)
}

func (task *Task) Log(loglevel log.LogLevel, format string, args ...interface{}) {
	task.DbTask().Log(loglevel, format, args...)
}

func (task *Task) Zombie(locks controller.Locks) bool {
	locks.RLockTask()
	task.mu.RLock()
	defer task.mu.RUnlock()
	defer locks.RUnlockTask()

	return task.zombie
}

func (task *Task) AAHP() bool {
	return task.allAlternativesHaveParent.Load()
}

func (task *Task) HasRunningNodeTasks() bool {
	task.mu.RLock()
	defer task.mu.RUnlock()

	return task.hasRunningNodeTasks()
}

func (task *Task) hasRunningNodeTasks() bool {
	for _, nodeTask := range task.nodeTasks {
		if nodeTask.DbNodeTask().State == "running" {
			return true
		}
	}

	return false
}

func (task *Task) GetNodeTaskList(locks controller.Locks) []*nodetask.NodeTask {
	locks.RLockTask()
	task.mu.RLock()
	defer task.mu.RUnlock()
	defer locks.RUnlockTask()

	nodeTasks := make([]*nodetask.NodeTask, 0, len(task.nodeTasks))
	for _, nodeTask := range task.nodeTasks {
		nodeTasks = append(nodeTasks, nodeTask)
	}
	// TODO: convert to copy

	return nodeTasks
}

func (task *Task) AddNodeTask(nodeTask *nodetask.NodeTask, locks controller.Locks) {
	locks.LockTask()
	task.mu.Lock()
	defer task.mu.Unlock()
	defer locks.UnlockTask()

	task.nodeTasks[nodeTask.DbNodeTask().Id] = nodeTask
}

func (task *Task) GetNodeTask(dbNodeTask *common.DbNodeTask, locks controller.Locks) *nodetask.NodeTask {
	locks.RLockTask()
	task.mu.RLock()
	defer task.mu.RUnlock()
	defer locks.RUnlockTask()

	controller.AssertLock(locks.Task, controller.LockRLocked)
	return task.nodeTasks[dbNodeTask.Id]
}

func (task *Task) AddChild(child *Task, locks controller.Locks) {
	dbTask := child.DbTask()

	locks.LockTask()
	task.mu.Lock()
	task.children[dbTask.DbId] = child
	task.mu.Unlock()
	locks.UnlockTask()
}

func (task *Task) Children(locks controller.Locks) []*Task {
	locks.RLockTask()
	task.mu.RLock()
	defer task.mu.RUnlock()
	defer locks.RUnlockTask()

	var children []*Task
	for _, child := range task.children {
		children = append(children, child)
	}

	return children
}

func (task *Task) SetData(zombie bool, allAlternativesHaveParent bool, privateData *string, locks controller.Locks) {
	locks.LockTask()
	task.mu.Lock()
	defer task.mu.Unlock()
	defer locks.UnlockTask()

	task.zombie = zombie
	task.allAlternativesHaveParent.Store(allAlternativesHaveParent)

	if task.privateData == nil {
		task.privateData = privateData
	}
}

func (task *Task) SetPrivateData(privateData *string, locks controller.Locks) {
	locks.LockTask()
	task.mu.Lock()
	defer task.mu.Unlock()
	defer locks.UnlockTask()

	task.privateData = privateData
}

// Remove sets the task for removal and returns true, if the task can be completely removed
func (task *Task) Remove(locks controller.Locks) bool {
	locks.LockTask()
	task.mu.Lock()
	defer task.mu.Unlock()
	defer locks.UnlockTask()

	task.zombie = true
	return len(task.nodeTasks) == 0
}

func (task *Task) RemoveNodeTask(dbNodeTask *common.DbNodeTask, locks controller.Locks) {
	locks.LockTask()
	task.mu.Lock()
	defer task.mu.Unlock()
	defer locks.UnlockTask()

	nodeTask := task.nodeTasks[dbNodeTask.Id]

	if nodeTask != nil {
		if dbNodeTask.State == "dummy" && nodeTask.DbNodeTask().State != "dummy" {
			// TODO: add unit test
			return
		}
		nodeTask.Remove(locks)
	}

	controller.AssertLock(locks.Task, controller.LockLocked)
	delete(task.nodeTasks, dbNodeTask.Id) // TODO function
}

func (task *Task) NodeTaskDelay(now time.Time, minDelay time.Duration, locks controller.Locks) (delay time.Duration) {
	locks.RLockTask()
	task.mu.RLock()
	for _, nodeTask := range task.nodeTasks {
		diff := now.Sub(nodeTask.DbNodeTask().CreationTime)
		if diff < minDelay {
			delay += minDelay - diff
		}
	}
	locks.RUnlockTask()
	task.mu.RUnlock()
	return
}

func (task *Task) ListIndex() int {
	return task.listIndex
}

func (task *Task) UpdateDbTaskPrivateData(locks controller.Locks) {
	locks.LockTask()
	task.mu.Lock()
	task.DbTask().PrivateData = task.privateData
	locks.UnlockTask()
	task.mu.Unlock()
}

func (task *Task) NodeTasksLen() int {
	task.mu.RLock()
	defer task.mu.RUnlock()

	return len(task.nodeTasks)
}
