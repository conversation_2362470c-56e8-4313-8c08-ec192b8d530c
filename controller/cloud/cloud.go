package cloud

import (
	"sync"
	"time"

	gocommon "git.moderntv.eu/go/common"
	"git.moderntv.eu/mcloud/log"
	"git.moderntv.eu/mcloud/system/common"
	"git.moderntv.eu/mcloud/system/controller"
	"git.moderntv.eu/mcloud/system/controller/node"
	"git.moderntv.eu/mcloud/system/controller/nodelist"
	"git.moderntv.eu/mcloud/system/controller/nodetask"
	"git.moderntv.eu/mcloud/system/controller/task"
)

type Cloud struct {
	TaskDispatchQueue chan TaskCommand
	FatalErrors       chan error
	tasksBump         chan bool

	// mu          sync.RWMutex
	controllers   map[string]*Controller
	controllersMu sync.RWMutex

	tasks    map[string]map[int]*task.Task // map[task.taskId]->map[task.id]->Task, task alternatives has the same taskId, but different id
	taskList task.TaskList                 // task queue ordered by task priority in which they should be handled
	tasksMu  sync.RWMutex                  // synchronizes both tasks and taskList, goroutine must not hold any Task.mu when locking this mutex

	nodes     map[string]*node.Node
	nodeLists map[string]*nodelist.NodeList // map from node resource ID to sorted (by amount of the resource) list of nodes
	nodesMu   sync.RWMutex                  // synchronizes both nodes and nodesLists
	metrics   *controller.Metrics
}

func NewCloud(metrics *controller.Metrics) *Cloud {
	cloud := &Cloud{
		TaskDispatchQueue: make(chan TaskCommand, 1024),
		FatalErrors:       make(chan error, 1),
		tasksBump:         make(chan bool, 1),
		controllers:       make(map[string]*Controller),
		tasks:             make(map[string]map[int]*task.Task),
		nodes:             make(map[string]*node.Node),
		nodeLists:         make(map[string]*nodelist.NodeList, 8),
		taskList: task.TaskList{
			Order: controller.DefaultTaskListOrder,
		},
		metrics: metrics,
	}

	go cloud.tasksHandler()

	return cloud
}

func (cloud *Cloud) Log(logLevel log.LogLevel, format string, args ...interface{}) {
	log.DataPrintf(log.Data{
		"loglevel": logLevel,
		"category": log.CategoryMcloudSystem,
		//"id":       self.controllerId,
	}, format, args...)
	gocommon.Log(format, args...)
}

func (cloud *Cloud) FatalError(err error) {
	select {
	case cloud.FatalErrors <- err:
	default:
	}
}

func (cloud *Cloud) GetController(id string) *Controller {
	cloud.controllersMu.RLock()
	defer cloud.controllersMu.RUnlock()

	return cloud.getController(id)
}
func (cloud *Cloud) getController(id string) *Controller {
	return cloud.controllers[id]
}

func (cloud *Cloud) GetControllers() []*Controller {
	cloud.controllersMu.RLock()
	defer cloud.controllersMu.RUnlock()

	controllers := make([]*Controller, 0, len(cloud.controllers))

	for _, controller := range cloud.controllers {
		controllers = append(controllers, controller)
	}

	return controllers
}

func (cloud *Cloud) GetTaskDb(dbTask *common.DbTask, locks controller.Locks) *task.Task {
	controller.AssertLock(locks.Task, controller.LockUnlocked)
	locks.RLockTasks()
	cloud.tasksMu.RLock()
	defer cloud.tasksMu.RUnlock()
	defer locks.RUnlockTasks()

	return cloud.getTask(dbTask.Id, dbTask.DbId, locks)
}

func (cloud *Cloud) GetTask(a string, b int, locks controller.Locks) *task.Task {
	controller.AssertLock(locks.Task, controller.LockUnlocked)
	locks.RLockTasks()
	cloud.tasksMu.RLock()
	defer cloud.tasksMu.RUnlock()
	defer locks.RUnlockTasks()

	return cloud.getTask(a, b, locks)
}

func (cloud *Cloud) getTask(taskID string, taskDbID int, locks controller.Locks) *task.Task {
	controller.AssertLock(locks.Tasks, controller.LockRLocked)
	inner, ok := cloud.tasks[taskID]
	if !ok {
		return nil
	}
	return inner[taskDbID]
}

func (cloud *Cloud) TaskIterator(filter func(*task.Task) bool, locks controller.Locks) <-chan *task.Task {
	controller.AssertLock(locks.Task, controller.LockUnlocked)
	locks.RLockTasks()
	cloud.tasksMu.RLock()
	l := len(cloud.tasks)
	cloud.tasksMu.RUnlock()
	locks.RUnlockTasks()

	/* buffered channel is only an optimization
	 * - sending to the channel can still block if the len(self.tasks) increases
	 * - this also means the caller has to read everything to avoid deadlock */
	ch := make(chan *task.Task, l+10) // add reserve to channel capacity

	go func() {
		locks := controller.NewLocks()
		controller.AssertLock(locks.Task, controller.LockUnlocked)
		locks.RLockTasks()
		cloud.tasksMu.RLock()
		defer cloud.tasksMu.RUnlock()
		defer locks.RUnlockTasks()

		controller.AssertLock(locks.Tasks, controller.LockRLocked)
		for _, tasks := range cloud.tasks {
			for _, task := range tasks {
				if filter(task) {
					ch <- task
				}
			}
		}
		close(ch)
	}()

	return ch
}

func (cloud *Cloud) GetTasks(id string, locks controller.Locks) map[int]*task.Task {
	controller.AssertLock(locks.Task, controller.LockUnlocked)
	locks.RLockTasks()
	cloud.tasksMu.RLock()
	defer cloud.tasksMu.RUnlock()
	defer locks.RUnlockTasks()

	return cloud.getTasks(id, locks)
}

func (cloud *Cloud) getTasks(id string, locks controller.Locks) map[int]*task.Task {
	controller.AssertLock(locks.Tasks, controller.LockRLocked)
	return cloud.tasks[id]
}

func (cloud *Cloud) TerminateTask(dbTask *common.DbTask, locks controller.Locks) {
	controller.AssertLock(locks.Task, controller.LockUnlocked)
	locks.LockTasks()
	cloud.tasksMu.Lock()
	defer cloud.tasksMu.Unlock()
	defer locks.UnlockTasks()

	cloud.terminateTask(dbTask, locks)
}
func (cloud *Cloud) terminateTask(dbTask *common.DbTask, locks controller.Locks) {
	dbTask.Action = "terminate"
	cloud.addTask(dbTask, false, locks)
}

func (cloud *Cloud) AddTask(dbTask *common.DbTask, zombie bool, locks controller.Locks) *task.Task {
	controller.AssertLock(locks.Task, controller.LockUnlocked)
	locks.LockTasks()
	cloud.tasksMu.Lock()
	defer cloud.tasksMu.Unlock()
	defer locks.UnlockTasks()

	return cloud.addTask(dbTask, zombie, locks)
}

func (cloud *Cloud) addTask(dbTask *common.DbTask, zombie bool, locks controller.Locks) *task.Task {
	var myTask = cloud.getTask(dbTask.Id, dbTask.DbId, locks)
	if myTask == nil {
		myTask = task.NewTask(zombie)
		controller.AssertLock(locks.Tasks, controller.LockLocked)
		taskGroup, ok := cloud.tasks[dbTask.Id]
		if !ok {
			taskGroup = make(map[int]*task.Task, 1)
			cloud.tasks[dbTask.Id] = taskGroup
		}
		taskGroup[dbTask.DbId] = myTask
	}
	myTask.SetDbTask(*dbTask)

	var aahp bool = true
	for _, alt := range cloud.getTasks(dbTask.Id, locks) {
		altDbTask := alt.DbTask()
		if altDbTask.After == nil && altDbTask.DependsOn == nil {
			aahp = false
		}
	}

	myTask.SetData(zombie, aahp, dbTask.PrivateData, locks)
	/*
		locks.LockTask()
		myTask.mu.Lock()
		myTask.zombie = zombie
		controller.AssertLock(locks.Task, controller.LockLocked)
		myTask.allAlternativesHaveParent.Store(aahp)
		if myTask.privateData == nil {
			myTask.privateData = dbTask.PrivateData
		}
		locks.UnlockTask()
		myTask.mu.Unlock()
	*/

	if dbTask.DependsOn != nil {
		parents := cloud.getTasks(*dbTask.DependsOn, locks)
		for _, parent := range parents {
			parent.AddChild(myTask, locks)
		}
	}

	cloud.taskListUpdate(myTask, locks)

	return myTask
}

func (cloud *Cloud) RemoveTask(dbTask *common.DbTask, locks controller.Locks) bool {
	locks.LockTasks()
	cloud.tasksMu.Lock()
	defer cloud.tasksMu.Unlock()
	defer locks.UnlockTasks()
	return cloud.removeTask(dbTask, locks)
}

func (cloud *Cloud) removeTask(dbTask *common.DbTask, locks controller.Locks) bool {
	task := cloud.getTask(dbTask.Id, dbTask.DbId, locks)
	if task == nil {
		return true
	}

	if !task.Remove(locks) {
		return false
	}

	controller.AssertLock(locks.Tasks, controller.LockLocked)
	cloud.taskList.Remove(task)

	dbTask = task.DbTask()
	a := cloud.tasks[dbTask.Id]
	if a != nil {
		controller.AssertLock(locks.Tasks, controller.LockLocked)
		delete(a, dbTask.DbId)
	}
	if len(a) == 0 {
		controller.AssertLock(locks.Tasks, controller.LockLocked)
		delete(cloud.tasks, dbTask.Id)
	}

	return true
}

func (cloud *Cloud) UpdateNode(dbNode *common.DbNode, locks controller.Locks) {
	locks.RLockNodes()
	cloud.nodesMu.RLock()
	defer cloud.nodesMu.RUnlock()
	defer locks.RUnlockNodes()

	controller.AssertLock(locks.Nodes, controller.LockRLocked)
	node := cloud.nodes[dbNode.Id]
	if node != nil {
		node.Update(dbNode.Enabled, dbNode.Capabilities, locks)
	}
}

func (cloud *Cloud) AddNode(dbNode *common.DbNode, locks controller.Locks) (*node.Node, bool) {
	locks.LockNodes()
	cloud.nodesMu.Lock()
	defer cloud.nodesMu.Unlock()
	defer locks.UnlockNodes()
	var added bool

	controller.AssertLock(locks.Nodes, controller.LockRLocked)
	myNode := cloud.nodes[dbNode.Id]
	if myNode != nil {
		added = myNode.SetDbNode(dbNode, locks)
	} else {
		myNode = node.NewNode(*dbNode)
		controller.AssertLock(locks.Nodes, controller.LockLocked)
		cloud.nodes[myNode.Id] = myNode
		added = true
	}

	//TODO do it 30 seconds after the message was sent, not received
	myNode.SetRemoveTimer(time.AfterFunc(controller.NodeRemoveTimeout, func() {
		locks := controller.NewLocks()
		if !myNode.Remove(locks) {
			return
		}

		locks.LockNodes()
		cloud.nodesMu.Lock()
		cloud.removeNode(myNode, locks)
		locks.UnlockNodes()
		cloud.nodesMu.Unlock()

		gocommon.Log("node '%s' removed by timer", myNode.Id)
	}), locks)

	myNode.UpdateResources(dbNode.FreeResources, dbNode.AvailableResources, locks)

	for res := range dbNode.FreeResources {
		controller.AssertLock(locks.Nodes, controller.LockLocked)
		nodeList := cloud.nodeLists[res]
		if nodeList == nil {
			nodeList = nodelist.NewNodeList(res)
			cloud.nodeLists[res] = nodeList
		}
		nodeList.Add(myNode)
	}

	return myNode, added
}

func (cloud *Cloud) removeNode(node_ *node.Node, locks controller.Locks) {
	node := cloud.getNode(node_.Id, locks)
	if node == nil {
		return
	}

	node.Remove(locks)
	controller.AssertLock(locks.Nodes, controller.LockLocked)
	delete(cloud.nodes, node.Id)

	for _, nodeList := range cloud.nodeLists {
		controller.AssertLock(locks.Nodes, controller.LockLocked)
		nodeList.Remove(node)
	}
}

func (cloud *Cloud) RemoveNode(node *node.Node, locks controller.Locks) {
	locks.LockNodes()
	cloud.nodesMu.Lock()
	defer cloud.nodesMu.Unlock()
	defer locks.UnlockNodes()

	cloud.removeNode(node, locks)
}

func (cloud *Cloud) GetNode(id string, locks controller.Locks) *node.Node {
	locks.RLockNodes()
	cloud.nodesMu.RLock()
	defer cloud.nodesMu.RUnlock()
	defer locks.RUnlockNodes()

	return cloud.getNode(id, locks)
}

func (cloud *Cloud) getNode(id string, locks controller.Locks) *node.Node {
	controller.AssertLock(locks.Nodes, controller.LockRLocked)
	return cloud.nodes[id]
}

func (cloud *Cloud) GetNodes(locks controller.Locks) []*node.Node {
	locks.RLockNodes()
	cloud.nodesMu.RLock()
	defer cloud.nodesMu.RUnlock()
	defer locks.RUnlockNodes()

	nodes := make([]*node.Node, 0, len(cloud.nodes))

	for _, node := range cloud.nodes {
		nodes = append(nodes, node)
	}

	return nodes
}

func (cloud *Cloud) FixNodeLists(node *node.Node, locks controller.Locks) {
	locks.LockNodes()
	cloud.nodesMu.Lock()
	controller.AssertLock(locks.Nodes, controller.LockLocked)
	for _, nodeList := range cloud.nodeLists {
		nodeList.Fix(node)
	}
	locks.UnlockNodes()
	cloud.nodesMu.Unlock()
}

func (cloud *Cloud) AddNodeTask(dbNodeTask *common.DbNodeTask, locks controller.Locks) (new bool) {
	locks.RLockNodes()
	cloud.nodesMu.RLock()
	myNode := cloud.nodes[dbNodeTask.Node]
	locks.RUnlockNodes()
	cloud.nodesMu.RUnlock()

	if myNode == nil {
		myNode = node.NewNode(common.DbNode{
			Id:   dbNodeTask.Node,
			Host: "<zombie>",
		}) //TODO

		locks.LockNodes()
		cloud.nodesMu.Lock()

		controller.AssertLock(locks.Nodes, controller.LockRLocked)
		existingNode := cloud.nodes[dbNodeTask.Node]
		if existingNode == nil {
			controller.AssertLock(locks.Nodes, controller.LockLocked)
			cloud.nodes[myNode.Id] = myNode
		}
		locks.UnlockNodes()
		cloud.nodesMu.Unlock()
	}

	var nodeTask *nodetask.NodeTask
	var newInstance bool
	var oldDbNodeTask *common.DbNodeTask
	nodeTask = myNode.GetNodeTask(dbNodeTask, locks)
	removeTimeout := controller.NodeTaskRemoveTimeout
	if dbNodeTask.State == "dummy" {
		removeTimeout = controller.DummyNodeTaskRemoveTimeout
	}

	if nodeTask != nil {
		oldDbNodeTask = nodeTask.SetDbNodeTask(*dbNodeTask, removeTimeout, locks)
		if oldDbNodeTask.State != "running" && dbNodeTask.State == "running" {
			defer cloud.HandleTasks()
		}
	} else {
		new = true

		nodeTask = nodetask.NewNodeTask(*dbNodeTask)
		nodeTask.SetRemoveTimer(time.AfterFunc(removeTimeout, func() {
			locks := controller.NewLocks()
			dbNodeTask := nodeTask.DbNodeTask()

			cloud.RemoveNodeTask(dbNodeTask, locks)
			if dbNodeTask.State != "dummy" {
				dbNodeTask.OriginTask.Log(log.LOG_WARN, "nodeTask '%s' removed by timer", dbNodeTask.Id)
			}
			go func() {
				// delay the handling in case multiple tasks time out to plan using their priority and not the order they time out
				time.Sleep(controller.NodeTaskRemoveTimeout) //TODO chan worker
				cloud.HandleTasks()
			}()
		}), locks)
	}

	if oldDbNodeTask == nil || oldDbNodeTask.StartTime != dbNodeTask.StartTime {
		newInstance = true
	}

	var task *task.Task
	if dbNodeTask.State != "dummy" {
		controller.AssertLock(locks.Task, controller.LockUnlocked)
		locks.LockTasks()
		cloud.tasksMu.Lock()
		task = cloud.getTask(dbNodeTask.OriginTask.Id, dbNodeTask.OriginTask.DbId, locks)
		if task == nil {
			task = cloud.addTask(&dbNodeTask.OriginTask.DbTask, true, locks)
		}
		locks.UnlockTasks()
		cloud.tasksMu.Unlock()

		if dbNodeTask.State == "starting" && newInstance {
			task.StartedCounter.Add(1)
		}
	}

	locks.AssertUnlocked()

	// update privateData in all alternatives
	if dbNodeTask.PrivateData != nil && dbNodeTask.State == "running" {
		locks.RLockTasks()
		cloud.tasksMu.RLock()
		alternatives := cloud.tasks[dbNodeTask.OriginTask.Id]
		for _, altTask := range alternatives {
			altTask.SetPrivateData(dbNodeTask.PrivateData, locks)
		}
		locks.RUnlockTasks()
		cloud.tasksMu.RUnlock()
	}

	locks.AssertUnlocked()
	if task != nil {
		task.AddNodeTask(nodeTask, locks)
		controller.AssertLock(locks.Task, controller.LockUnlocked)
		locks.LockTasks()
		cloud.tasksMu.Lock()
		cloud.taskListUpdate(task, locks)
		locks.UnlockTasks()
		cloud.tasksMu.Unlock()
	}

	myNode.AddNodeTask(nodeTask, locks)

	// updated NodeTask can change the order of Nodes
	// XXX: remove fixNodeLists call from addDummyNodeTask if this is uncommented
	//self.nodeList.Fix(node)

	return
}

func (cloud *Cloud) taskListUpdate(task *task.Task, locks controller.Locks) {
	isTaskRunning, nodeTasks, _ := task.GetRunning(controller.CountAnyState, locks)

	controller.AssertLock(locks.Tasks, controller.LockLocked)
	if task.Runnable() {
		if !isTaskRunning {
			cloud.taskList.Add(task)
		} else {
			cloud.taskList.Remove(task)
		}
	} else {
		if nodeTasks == 0 {
			cloud.taskList.Remove(task)
		} else {
			cloud.taskList.Add(task)
		}
	}
}

func (cloud *Cloud) TaskListIndex(task *task.Task, locks controller.Locks) int {
	controller.AssertLock(locks.Task, controller.LockUnlocked)
	locks.RLockTasks()
	cloud.tasksMu.RLock()
	defer cloud.tasksMu.RUnlock()
	defer locks.RUnlockTasks()

	return task.ListIndex()
}

func (cloud *Cloud) RemoveNodeTask(dbNodeTask *common.DbNodeTask, locks controller.Locks) {
	task := cloud.GetTask(dbNodeTask.OriginTask.Id, dbNodeTask.OriginTask.DbId, locks)
	if task != nil {
		task.RemoveNodeTask(dbNodeTask, locks)

		controller.AssertLock(locks.Task, controller.LockUnlocked)
		locks.LockTasks()
		cloud.tasksMu.Lock()
		cloud.taskListUpdate(task, locks)
		locks.UnlockTasks()
		cloud.tasksMu.Unlock()
	}

	node := cloud.GetNode(dbNodeTask.Node, locks)
	if node != nil {
		node.RemoveNodeTask(dbNodeTask, locks)
	}
}

func (cloud *Cloud) IsTaskIdRunning(id string, flags uint8, locks controller.Locks) bool {
	controller.AssertLock(locks.Task, controller.LockUnlocked)
	locks.RLockTasks()
	cloud.tasksMu.RLock()
	defer cloud.tasksMu.RUnlock()
	defer locks.RUnlockTasks()
	return cloud.isTaskIdRunning(id, flags, locks)
}

func (cloud *Cloud) isTaskIdRunning(id string, flags uint8, locks controller.Locks) bool {
	controller.AssertLock(locks.Tasks, controller.LockRLocked)
	for _, task := range cloud.tasks[id] {
		if flags&controller.CountAnyLevel == 0 && task.DbTask().Level > 0 {
			continue
		}
		controller.AssertLock(locks.Task, controller.LockUnlocked)
		if running, _, _ := task.GetRunning(flags, locks); running {
			return true
		}
	}

	return false
}

type TaskCommand struct {
	Task        common.RunnableTask
	Destination string
	TargetTime  *time.Time
}

type Controller struct {
	common.DbController

	mu     sync.RWMutex
	remove *time.Timer
}

func NewController(dbController common.DbController) *Controller {
	return &Controller{
		DbController: dbController,
	}
}

func (cloud *Cloud) AddController(dbController *common.DbController) bool {
	cloud.controllersMu.Lock()
	defer cloud.controllersMu.Unlock()
	if dbController == nil {
		panic("AddController called with nil dbController")
	}

	var added bool

	myController := cloud.controllers[dbController.Id]
	if myController != nil {
		myController.DbController = *dbController

		if myController.remove != nil { //TODO do a reset instead
			myController.remove.Stop()
			myController.remove = nil
		}
	} else {
		myController = NewController(*dbController)
		cloud.controllers[myController.Id] = myController
		added = true
	}

	myController.remove = time.AfterFunc(controller.ControllerRemoveTimeout, func() {
		cloud.RemoveController(myController)
		gocommon.Log("controller '%s' removed by timer", myController.Id)
	})

	return added
}

func (cloud *Cloud) RemoveController(controller_ *Controller) {
	cloud.controllersMu.Lock()
	defer cloud.controllersMu.Unlock()

	controller := cloud.getController(controller_.Id)
	if controller == nil {
		return
	}

	controller.mu.Lock()
	if controller.remove != nil {
		controller.remove.Stop()
		controller.remove = nil
	}
	controller.mu.Unlock()

	delete(cloud.controllers, controller.Id)
}
