package cloud

import (
	"bytes"
	"encoding/json"
	"fmt"
	"testing"
	"time"

	. "git.moderntv.eu/go/test"
	"git.moderntv.eu/mcloud/log"
	"git.moderntv.eu/mcloud/system/common"
	"git.moderntv.eu/mcloud/system/controller"
	"git.moderntv.eu/mcloud/system/controller/node"
	"git.moderntv.eu/mcloud/system/controller/task"
)

func init() {
	log.SetLogger(&log.NullLogger{})
}

var (
	currentDbId int
)

func DbId() int {
	currentDbId++
	return currentDbId
}

func NewDbNode(data []byte) (node *common.DbNode, err error) {
	err = json.Unmarshal(data, &node)
	if err != nil {
		return
	}

	return
}

func assertAllUnlocked(locks controller.Locks) {
	controller.AssertLock(locks.Controller, controller.LockUnlocked)
	controller.AssertLock(locks.Node, controller.LockUnlocked)
	controller.AssertLock(locks.NodeTask, controller.LockUnlocked)
	controller.AssertLock(locks.Nodes, controller.LockUnlocked)
	controller.AssertLock(locks.Task, controller.LockUnlocked)
	controller.AssertLock(locks.Tasks, controller.LockUnlocked)
}

func TestNodeSelection(t *testing.T) {
	t.Run("non-existent and disabled node", func(t *testing.T) {
		cloud := NewCloud(controller.NewMetrics())

		taskBad := createTask(t, cloud, &common.DbTask{
			DbId:   DbId(),
			Id:     "task_id_bad",
			Action: "run",
		})

		locks := controller.NewLocks()

		_, _, err := cloud.SelectNode(taskBad, nil, locks)
		ASSERT(t, err != nil, "selected node even if there are non")

		node := createNode(t, cloud, common.MultiResources{"CPU": []float64{50.0}}, nil, "testhost")
		dbNode := common.DbNode{
			Id:      node.Id,
			Enabled: false,
		}
		cloud.UpdateNode(&dbNode, locks)

		_, _, err = cloud.SelectNode(taskBad, nil, locks)
		ASSERT(t, err != nil, "selected node even if it is not enabled")
		assertAllUnlocked(locks)
	})
	t.Run("selection by resources", func(t *testing.T) {
		cloud := NewCloud(controller.NewMetrics())
		locks := controller.NewLocks()

		_ = createNode(t, cloud, common.MultiResources{
			"RES_A": []float64{50.0},
			"RES_B": []float64{0, 1000},
		}, nil, "testhostA")
		var nodeB = createNode(t, cloud, common.MultiResources{
			"RES_A": []float64{100.0},
			"RES_B": []float64{0, 1000, 2000},
		}, nil, "testhostB")
		_ = createNode(t, cloud, common.MultiResources{
			"RES_A": []float64{50.0},
			"RES_B": []float64{1000, 1000, 2000},
		}, nil, "testhostC")
		var nodeD = createNode(t, cloud, common.MultiResources{
			"RES_A": []float64{200.0},
			"RES_B": []float64{0, 1000},
		}, nil, "testhostD")

		// selection of node with the most resources
		taskOk1 := createTask(t, cloud, &common.DbTask{
			DbId:              DbId(),
			Id:                "task_ok1",
			Action:            "run",
			RequiredResources: &common.Resources{"RES_A": 50.0},
		})
		selectedNode, _, err := cloud.SelectNode(taskOk1, nil, locks)
		OK(t, err)
		ASSERT(t, selectedNode != nil, "cloud.SelectNode returned nil Node without an error")
		ASSERT(t, selectedNode.Id == nodeD.Id, fmt.Sprintf("selected node %s instead of %s", selectedNode.Id, nodeD.Id))
		// reserve resources on the node with the most resources
		taskRes := createTask(t, cloud, &common.DbTask{
			DbId:              DbId(),
			Id:                "task_reserver",
			Action:            "run",
			RequiredResources: &common.Resources{"RES_A": 150.0},
		})
		_, err = cloud.HandleTask(taskRes, nil, locks)
		OK(t, err)
		assertAllUnlocked(locks)
		// now a different node should be selected
		taskOk2 := createTask(t, cloud, &common.DbTask{
			DbId:              DbId(),
			Id:                "task_ok2",
			Action:            "run",
			RequiredResources: &common.Resources{"RES_A": 50.0},
		})
		selectedNode, _, err = cloud.SelectNode(taskOk2, nil, locks)
		OK(t, err)
		ASSERT(t, selectedNode != nil, "cloud.SelectNode returned nil Node without an error")
		ASSERT(t, selectedNode.Id == nodeB.Id, fmt.Sprintf("selected node %s instead of %s", selectedNode.Id, nodeB.Id))

		// usage of the most scarce resource for the selection
		taskOk3 := createTask(t, cloud, &common.DbTask{
			DbId:              DbId(),
			Id:                "task_ok3",
			Action:            "run",
			RequiredResources: &common.Resources{"RES_A": 50.0, "RES_B": 1000},
		})
		//var selectedResources common.SelectedResources
		//selectedNode, selectedResources, err = cloud.SelectNode(taskOk3, nil)
		selectedNode, _, err = cloud.SelectNode(taskOk3, nil, locks)
		OK(t, err)
		ASSERT(t, selectedNode != nil, "cloud.SelectNode returned nil Node without an error")
		ASSERT(t, selectedNode.Id == nodeB.Id, fmt.Sprintf("selected node %s instead of %s", selectedNode.Id, nodeB.Id))
		//ASSERT(t, selectedResources["RES_B"] == 2, "selected wrong resource index (%d != %d)", selectedResources["RES_B"], 2)

		// bad task
		taskBad := createTask(t, cloud, &common.DbTask{
			DbId:              DbId(),
			Id:                "task_id_bad",
			Action:            "run",
			RequiredResources: &common.Resources{"RES_A": 300.0},
		})
		selectedNode, _, err = cloud.SelectNode(taskBad, nil, locks)
		assertAllUnlocked(locks)
		ASSERT(t, err != nil, "selected node even it has not resources for the task")
	})
	t.Run("selection with requirements", func(t *testing.T) {
		cloud := NewCloud(controller.NewMetrics())
		locks := controller.NewLocks()
		// nodes
		_ = createNode(t, cloud, common.MultiResources{"CPU": []float64{100.0}}, &common.Flags{"test": false}, "testhostA")
		_ = createNode(t, cloud, common.MultiResources{"CPU": []float64{100.0}}, nil, "testhostB")
		nodeC := createNode(t, cloud, common.MultiResources{"CPU": []float64{100.0}}, &common.Flags{"test": true}, "testhostC")

		// ok task
		taskOk := createTask(t, cloud, &common.DbTask{
			DbId:         DbId(),
			Id:           "task_ok",
			Action:       "run",
			Requirements: &common.Flags{"test": true},
		})
		selectedNode, _, err := cloud.SelectNode(taskOk, nil, locks)
		OK(t, err)
		ASSERT(t, selectedNode.Id == nodeC.Id, fmt.Sprintf("selected node %s instead of %s", selectedNode.Id, nodeC.Id))

		// bad task
		taskBad := createTask(t, cloud, &common.DbTask{
			DbId:         DbId(),
			Id:           "task_bad",
			Action:       "run",
			Requirements: &common.Flags{"test_nonexistent": true},
		})
		selectedNode, _, err = cloud.SelectNode(taskBad, nil, locks)
		assertAllUnlocked(locks)
		ASSERT(t, err != nil, "selected node even when there is non that has capabilities for the task")
	})
	t.Run("not yet running task reservation", func(t *testing.T) {
		cloud := NewCloud(controller.NewMetrics())
		locks := controller.NewLocks()
		// nodes
		_ = createNode(t, cloud, common.MultiResources{"CPU": []float64{100.0, 10.0}}, &common.Flags{"test": false}, "testhostA")

		// ok task
		taskOk := createTask(t, cloud, &common.DbTask{
			DbId:              DbId(),
			Id:                "task_ok",
			Action:            "run",
			RequiredResources: &common.Resources{"CPU": 95.0},
		})
		_, err := cloud.HandleTask(taskOk, nil, locks)
		OK(t, err)
		assertAllUnlocked(locks)

		// bad task
		taskBad := createTask(t, cloud, &common.DbTask{
			DbId:              DbId(),
			Id:                "task_bad",
			Action:            "run",
			RequiredResources: &common.Resources{"CPU": 11.0},
		})
		_, err = cloud.HandleTask(taskBad, nil, locks)
		assertAllUnlocked(locks)
		ASSERT(t, err != nil, "schuduler task for which the node should not have resources")
	})
	t.Run("multi-resource reservations", func(t *testing.T) {
		cloud := NewCloud(controller.NewMetrics())
		locks := controller.NewLocks()
		myNode := createNode(t, cloud, common.MultiResources{
			"CPU":     []float64{20},
			"GPU_enc": []float64{10, 100},
			"GPU_mem": []float64{100, 50},
		}, &common.Flags{"test": false}, "testhostA")

		// ok task
		taskOk := createTask(t, cloud, &common.DbTask{
			DbId:              DbId(),
			Id:                "task_ok",
			Action:            "run",
			RequiredResources: &common.Resources{"CPU": 10, "GPU_enc": 50, "GPU_mem": 10},
		})
		_, err := cloud.HandleTask(taskOk, nil, locks)
		OK(t, err)
		assertAllUnlocked(locks)
		type p struct {
			Resource string
			Index    int
		}
		taskCommand := <-cloud.TaskDispatchQueue
		var index int
		var present bool
		for _, resource := range []p{{"CPU", 0}, {"GPU_enc", 1}, {"GPU_mem", 1}} {
			index, present = taskCommand.Task.SelectedResources[resource.Resource]
			ASSERT(t, present, fmt.Sprintf("no selected resource '%s'", resource.Resource))
			ASSERT(t, index == resource.Index, fmt.Sprintf("bad selected resource '%s': %d != %d", resource.Resource, index, resource.Index))
		}

		// 50 of GPU_enc is reserved by task_ok
		taskBad := createTask(t, cloud, &common.DbTask{
			DbId:              DbId(),
			Id:                "task_bad",
			Action:            "run",
			RequiredResources: &common.Resources{"CPU": 10, "GPU_enc": 51},
		})
		_, err = cloud.HandleTask(taskBad, nil, locks)
		ASSERT(t, err != nil, "scheduled task for which the node should not have resources")

		now := time.Now()
		startTime := now.Add(-10 * time.Minute)
		dbNodeTask := common.DbNodeTask{
			Id:               "nodetask_id",
			Task:             taskOk.DbTask().DbId,
			Node:             myNode.Id,
			State:            "running",
			LastOnlineTime:   &now,
			LastRunningTime:  &now,
			StartTime:        &startTime,
			RunningSinceTime: &startTime,
			OriginTask:       taskCommand.Task,
			ResourceUsage:    common.MultiResources{"CPU": []float64{10.0}, "GPU_enc": []float64{0, 25}},
		}
		ASSERT(t, !node.NeedsReservedResources(&dbNodeTask), "dbNodeTask should not have full resource reservation")
		cloud.AddNodeTask(&dbNodeTask, locks)

		// 25 of GPU_enc is reserved as a maximum reservation
		taskBad2 := createTask(t, cloud, &common.DbTask{
			DbId:              DbId(),
			Id:                "task_bad2",
			Action:            "run",
			RequiredResources: &common.Resources{"CPU": 10, "GPU_enc": 76},
		})
		_, err = cloud.HandleTask(taskBad2, nil, locks)
		ASSERT(t, err != nil, "scheduled task for which the node should not have resources")

		// ok task - should fit in the remaining resources at index 1
		taskOk2 := createTask(t, cloud, &common.DbTask{
			DbId:              DbId(),
			Id:                "task_ok2",
			Action:            "run",
			RequiredResources: &common.Resources{"CPU": 10, "GPU_enc": 50, "GPU_mem": 10},
		})
		_, err = cloud.HandleTask(taskOk2, nil, locks)
		assertAllUnlocked(locks)
		OK(t, err)
	})
	t.Run("new task resource reservation", func(t *testing.T) {
		t.Skip("test not implemented")
		//TODO
		/*if !cloud.IsTaskIdRunning(dbTask.Id, 0) {
			t.Fatalf("task not reported as running")
		}*/
	})
	t.Run("restarting task resource reservation", func(t *testing.T) {
		t.Skip("test not implemented")
		//TODO
	})
	t.Run("maximum running task resource reservation", func(t *testing.T) {
		cloud := NewCloud(controller.NewMetrics())
		locks := controller.NewLocks()

		myNode := createNode(t, cloud, common.MultiResources{"CPU": []float64{150.0, 10.0}}, nil, "id_test")

		for i := 0; i < 3; i++ {
			id := fmt.Sprintf("task_id_%d", i)
			dbTask := common.DbTask{
				DbId:              DbId(),
				Id:                id,
				Action:            "run",
				RequiredResources: &common.Resources{"CPU": 200.0},
			}
			_ = createTask(t, cloud, &dbTask)

			now := time.Now()
			start := now.Add(-1 * time.Hour)
			idn := fmt.Sprintf("nodetask_of_%d_on_%s", i, "node_id_test")
			dbNodeTask := common.DbNodeTask{
				Id:               idn,
				Task:             i,
				Node:             myNode.Id,
				State:            "running",
				LastOnlineTime:   &now,
				LastRunningTime:  &now,
				StartTime:        &start,
				RunningSinceTime: &start,
				OriginTask: common.RunnableTask{
					DbTask:            dbTask,
					SelectedResources: common.SelectedResources{"CPU": 0},
				},
				ResourceUsage: common.MultiResources{"CPU": []float64{50.0, 0.0}},
			}
			ASSERT(t, !node.NeedsReservedResources(&dbNodeTask), "dbNodeTask should not have full resource reservation")
			cloud.AddNodeTask(&dbNodeTask, locks)
		}

		// ok task
		taskOk := createTask(t, cloud, &common.DbTask{
			DbId:              DbId(),
			Id:                "task_ok",
			Action:            "run",
			RequiredResources: &common.Resources{"CPU": 10},
		})
		_, err := cloud.HandleTask(taskOk, nil, locks)
		OK(t, err)

		// bad task - requires more than available at any index
		taskBad := createTask(t, cloud, &common.DbTask{
			DbId:              DbId(),
			Id:                "task_bad",
			Action:            "run",
			RequiredResources: &common.Resources{"CPU": 11.0},
		})
		_, err = cloud.HandleTask(taskBad, nil, locks)
		assertAllUnlocked(locks)
		ASSERT(t, err != nil, "task handled even when there is no node for it")
	})
	t.Run("node preference for child tasks", func(t *testing.T) {
		cloud := NewCloud(controller.NewMetrics())
		locks := controller.NewLocks()

		nodeMax := createNode(t, cloud, common.MultiResources{"GPU": []float64{110.0}}, &common.Flags{"X": true}, "testhostA")
		nodeMax.Group = "X"
		nodeB := createNode(t, cloud, common.MultiResources{"GPU": []float64{100.0}}, nil, "testhostB")
		nodeB.Group = "Y"
		nodeC := createNode(t, cloud, common.MultiResources{"GPU": []float64{105.0}}, &common.Flags{"X": true}, "testhostC")
		nodeC.Group = "Y"

		parentDbTask := common.DbTask{
			DbId:   DbId(),
			Id:     "task_id_parent",
			Action: "run",
		}
		_ = createTask(t, cloud, &parentDbTask)

		now := time.Now()
		start := now.Add(-1 * time.Hour)
		idn := fmt.Sprintf("nodetask_of_%s_on_%s", parentDbTask.Id, nodeB.Id)
		dbNodeTask := common.DbNodeTask{
			Id:               idn,
			Task:             parentDbTask.DbId,
			Node:             nodeB.Id,
			State:            "running",
			LastOnlineTime:   &now,
			LastRunningTime:  &now,
			StartTime:        &start,
			RunningSinceTime: &start,
			OriginTask: common.RunnableTask{
				DbTask: parentDbTask,
			},
		}
		ASSERT(t, !node.NeedsReservedResources(&dbNodeTask), "dbNodeTask should not have full resource reservation")
		cloud.AddNodeTask(&dbNodeTask, locks)

		// for children selection testing
		nonRunningParentTaskId := "task_id_not_running_parent"
		runningChildDbTask := common.DbTask{
			DbId:      DbId(),
			Id:        "task_id_running_child",
			Action:    "run",
			DependsOn: &nonRunningParentTaskId,
		}
		_ = createTask(t, cloud, &runningChildDbTask)
		idn = fmt.Sprintf("nodetask_of_%s_on_%s", parentDbTask.Id, nodeB.Id)
		dbNodeTask = common.DbNodeTask{
			Id:               idn,
			Task:             runningChildDbTask.DbId,
			Node:             nodeB.Id,
			State:            "running",
			LastOnlineTime:   &now,
			LastRunningTime:  &now,
			StartTime:        &start,
			RunningSinceTime: &start,
			OriginTask: common.RunnableTask{
				DbTask: runningChildDbTask,
			},
		}
		ASSERT(t, !node.NeedsReservedResources(&dbNodeTask), "dbNodeTask should not have full resource reservation")
		cloud.AddNodeTask(&dbNodeTask, locks)

		t.Run("same node as parent", func(t *testing.T) {
			taskOk := createTask(t, cloud, &common.DbTask{
				DbId:              DbId(),
				Id:                "task_ok",
				Action:            "run",
				RequiredResources: &common.Resources{"GPU": 100.0},
				DependsOn:         &parentDbTask.Id,
			})

			selectedNode, _, err := cloud.SelectNode(taskOk, nil, locks)
			OK(t, err)
			ASSERT(t, selectedNode.Id == nodeB.Id, fmt.Sprintf("selected node %s instead of %s where the parent is running", selectedNode.Id, nodeB.Id))
		})
		t.Run("same node as children", func(t *testing.T) {
			if !controller.PreferNodesWithChildren {
				t.Skip()
			}
			nonRunningParentTask := createTask(t, cloud, &common.DbTask{
				DbId:              DbId(),
				Id:                nonRunningParentTaskId,
				Action:            "run",
				RequiredResources: &common.Resources{"GPU": 10.0},
			})
			_ = createTask(t, cloud, &runningChildDbTask) // to have complete tree information
			selectedNode, _, err := cloud.SelectNode(nonRunningParentTask, nil, locks)
			OK(t, err)
			ASSERT(t, selectedNode.Id == nodeB.Id, fmt.Sprintf("selected node %s instead of %s where the child is running", selectedNode.Id, nodeB.Id))

			nodeNew := createNode(t, cloud, common.MultiResources{"GPU": []float64{200.0}}, nil, "testhostD")
			nodeNew.Group = "Z"
			defer cloud.RemoveNode(nodeNew, locks)
			selectedNode, _, err = cloud.SelectNode(nonRunningParentTask, nil, locks)
			OK(t, err)
			ASSERT(t, selectedNode.Id == nodeNew.Id, fmt.Sprintf("selected node %s even if it results with an imbalance in resources", selectedNode.Id))
		})
		t.Run("same node group as parent", func(t *testing.T) {
			taskOk := createTask(t, cloud, &common.DbTask{
				DbId:              DbId(),
				Id:                "task_ok3",
				Action:            "run",
				RequiredResources: &common.Resources{"GPU": 101.0},
				DependsOn:         &parentDbTask.Id,
			})

			selectedNode, _, err := cloud.SelectNode(taskOk, nil, locks)
			OK(t, err)
			ASSERT(t, selectedNode.Id == nodeC.Id, fmt.Sprintf("selected node %s instead of %s in the same node group as parent", selectedNode.Id, nodeB.Id))
		})
		t.Run("after parent to best node", func(t *testing.T) {
			task := createTask(t, cloud, &common.DbTask{
				DbId:              DbId(),
				Id:                "task_ok4",
				Action:            "run",
				RequiredResources: &common.Resources{"GPU": 100.0},
				After:             &parentDbTask.Id,
			})
			selectedNode, _, err := cloud.SelectNode(task, nil, locks)
			OK(t, err)
			ASSERT(t, selectedNode.Id == nodeMax.Id, fmt.Sprintf("selected node %s instead of %s with the most resources", selectedNode.Id, nodeMax.Id))
		})
		t.Run("same node group as children", func(t *testing.T) {
			if !controller.PreferNodesWithChildren {
				t.Skip()
			}
			nonRunningParentTask := createTask(t, cloud, &common.DbTask{
				DbId:              DbId(),
				Id:                nonRunningParentTaskId,
				Action:            "run",
				RequiredResources: &common.Resources{"GPU": 10.0},
				Requirements:      &common.Flags{"X": true},
			})
			_ = createTask(t, cloud, &runningChildDbTask)
			selectedNode, _, err := cloud.SelectNode(nonRunningParentTask, nil, locks)
			OK(t, err)
			ASSERT(t, selectedNode.Id == nodeC.Id, fmt.Sprintf("selected node %s instead of %s in the same node group as the child", selectedNode.Id, nodeC.Id))
		})
		t.Run("parents not running", func(t *testing.T) {
			deletedId := "task_id_deleted"
			goodTaskA := createTask(t, cloud, &common.DbTask{
				DbId:      DbId(),
				Id:        "task_goodA",
				Action:    "run",
				Finite:    true,
				DependsOn: &deletedId, // deleted => finished
			})
			badTaskA := createTask(t, cloud, &common.DbTask{
				DbId:      DbId(),
				Id:        "task_badA",
				Action:    "run",
				DependsOn: &nonRunningParentTaskId,
			})
			badTaskB := createTask(t, cloud, &common.DbTask{
				DbId:   DbId(),
				Id:     "task_badB",
				Action: "run",
				After:  &nonRunningParentTaskId,
			})
			badTaskC := createTask(t, cloud, &common.DbTask{
				DbId:      DbId(),
				Id:        "task_badC",
				Action:    "run",
				DependsOn: &nonRunningParentTaskId,
				After:     &nonRunningParentTaskId,
			})
			badTaskD := createTask(t, cloud, &common.DbTask{
				DbId:      DbId(),
				Id:        "task_badD",
				Action:    "run",
				DependsOn: &nonRunningParentTaskId,
				After:     &deletedId,
			})

			var err error
			var ran bool

			ran, err = cloud.HandleTask(goodTaskA, nil, locks)
			ASSERT(t, err == nil && ran, "task depending on a finished parent not dispatched (error: %s)\n", err)

			ran, err = cloud.HandleTask(badTaskA, nil, locks)
			ASSERT(t, err == nil, "error: %s\n", err)
			ASSERT(t, !ran, "dispatched task without a running parent")

			ran, err = cloud.HandleTask(badTaskB, nil, locks)
			ASSERT(t, err == nil, "error: %s\n", err)
			ASSERT(t, !ran, "dispatched task without a running parent")

			ran, err = cloud.HandleTask(badTaskC, nil, locks)
			ASSERT(t, err == nil, "error: %s\n", err)
			ASSERT(t, !ran, "dispatched task without a running parent")

			ran, err = cloud.HandleTask(badTaskD, nil, locks)
			ASSERT(t, err == nil, "error: %s\n", err)
			ASSERT(t, !ran, "dispatched task without a running parent")
		})
		t.Run("require parents done", func(t *testing.T) {
			badTask := createTask(t, cloud, &common.DbTask{
				DbId:                 DbId(),
				Id:                   "task_badE",
				Action:               "run",
				Finite:               true,
				RequiresFinishedDeps: true,
				DependsOn:            &parentDbTask.Id, // running => not finished
				After:                &parentDbTask.Id,
			})

			ran, err := cloud.HandleTask(badTask, nil, locks)
			ASSERT(t, err == nil, "error: %s\n", err)
			ASSERT(t, !ran, "dispatched task before parent finished")
		})
		assertAllUnlocked(locks)
	})
}

func TestTaskDispatching(t *testing.T) {
	t.Run("basic", func(t *testing.T) {
		cloud := NewCloud(controller.NewMetrics())
		locks := controller.NewLocks()
		// nodes
		nodeA := createNode(t, cloud, common.MultiResources{"CPU": []float64{100.0}}, &common.Flags{"test": true}, "testhostA")
		// ok task
		taskOk := createTask(t, cloud, &common.DbTask{
			DbId:              DbId(),
			Id:                "task_ok",
			Action:            "run",
			Requirements:      &common.Flags{"test": true},
			RequiredResources: &common.Resources{"CPU": 100.0},
		})
		_, err := cloud.HandleTask(taskOk, nil, locks)
		OK(t, err)
		select {
		case taskCommand := <-cloud.TaskDispatchQueue:
			ASSERT(t, taskCommand.Task.Id == taskOk.DbTask().Id, fmt.Sprintf("dispatched task %v instead of %v", taskCommand.Task.Id, taskOk)) //TODO check unique id with revision and alternative
			ASSERT(t, taskCommand.Destination == nodeA.Host, fmt.Sprintf("dispatched task on node %v instead of %v", taskCommand.Destination, nodeA.Id))
		}
		assertAllUnlocked(locks)
	})
	t.Run("priority and order", func(t *testing.T) {
		//TODO priority
		var cloud = NewCloud(controller.NewMetrics())
		locks := controller.NewLocks()

		var nodeA = createNode(t, cloud, common.MultiResources{"CPU": []float64{0.0}}, nil, "testhostA")

		var CT = func(tb testing.TB, name string, prio int, order int, hasAfter bool) *task.Task {
			tb.Helper()

			var now = time.Now()
			var start = now.Add(-1 * time.Hour)

			var parentTask *task.Task
			if hasAfter {
				parentTask = createTask(t, cloud, &common.DbTask{
					DbId:   DbId(),
					Order:  1,
					Id:     fmt.Sprintf("task_parent_%s_%d", name, order),
					Action: "run",
				})
				var dbNodeTask = common.DbNodeTask{
					Id:               fmt.Sprintf("nodetask_%s", parentTask.DbTask().Id),
					Task:             parentTask.DbTask().DbId,
					Node:             nodeA.Id,
					State:            "running",
					LastOnlineTime:   &now,
					LastRunningTime:  &now,
					StartTime:        &start,
					RunningSinceTime: &start,
					OriginTask: common.RunnableTask{
						DbTask: *parentTask.DbTask(),
					},
				}
				ASSERT(t, !node.NeedsReservedResources(&dbNodeTask), "dbNodeTask should not have full resource reservation")
				cloud.AddNodeTask(&dbNodeTask, locks)
			}
			var alt = fmt.Sprintf("alternative_%s_%d", name, order)
			var dbTask = common.DbTask{
				DbId:        DbId(),
				Order:       order,
				Priority:    &prio,
				Id:          fmt.Sprintf("task_%s", name),
				Action:      "run",
				Alternative: &alt,
			}
			if parentTask != nil {
				dbTask.After = &parentTask.DbTask().Id
			}
			return createTask(t, cloud, &dbTask)
		}
		// tasks without check parents
		var taskA1 = CT(t, "A", 100, 1, false)
		var taskA2 = CT(t, "A", 100, 2, false)
		var taskB1 = CT(t, "B", 50, 1, false)
		var taskB2 = CT(t, "B", 50, 2, false)

		// task with a partial check
		var taskC1 = CT(t, "C", 40, 1, true)
		var taskC2 = CT(t, "C", 40, 2, false)
		var taskD1 = CT(t, "D", 30, 1, false)
		var taskD2 = CT(t, "D", 30, 2, true)

		// task with complete checks
		var taskE1 = CT(t, "E", 20, 1, true)
		var taskE2 = CT(t, "E", 20, 2, true)
		var taskF1 = CT(t, "F", 10, 1, true)
		var taskF2 = CT(t, "F", 10, 2, true)

		// tasks with checks
		var expectedOrder []*task.Task
		switch cloud.taskList.Order {
		case controller.TaskListOrderAutomatic:
			expectedOrder = []*task.Task{
				taskA1, taskA2,
				taskB1, taskB2,
				taskC1, taskC2,
				taskD1, taskD2,

				taskE1,
				taskF1,

				taskE2,
				taskF2,
			}
		case controller.TaskListOrderHorizontal:
			expectedOrder = []*task.Task{
				taskA1, taskA2,
				taskB1, taskB2,
				taskC1, taskC2,
				taskD1, taskD2,
				taskE1, taskE2,
				taskF1, taskF2,
			}
		case controller.TaskListOrderVertical:
			expectedOrder = []*task.Task{
				taskA1,
				taskB1,
				taskC1,
				taskD1,
				taskE1,
				taskF1,

				taskA2,
				taskB2,
				taskC2,
				taskD2,
				taskE2,
				taskF2,
			}
		default:
			t.Fatalf("invalid TaskList order")
		}

		cloud.HandleTasks()

		var timeout = time.After(5 * time.Second)
		for i := 0; i < len(expectedOrder); i++ {
			select {
			case taskCommand := <-cloud.TaskDispatchQueue:
				ASSERT(t,
					taskCommand.Task.DbId == expectedOrder[i].DbTask().DbId,
					fmt.Sprintf("dispatched task %s(%s) instead of %s(%s)",
						taskCommand.Task.Id,
						*taskCommand.Task.Alternative,
						expectedOrder[i].DbTask().Id,
						*expectedOrder[i].DbTask().Alternative,
					),
				)
			case <-timeout:
				t.Fatalf("timed out")
			}
		}

		assertAllUnlocked(locks)
	})
	t.Run("delay of higher order alternatives", func(t *testing.T) {
		t.Skip("test not implemented")
		//TODO
	})
	t.Run("rerun NodeTask on timer removal", func(t *testing.T) {
		t.Skip("test not implemented")
		//TODO
	})
	t.Run("dispatch of finite tasks", func(t *testing.T) {
		t.Run("parent finished", func(t *testing.T) {
			cloud := NewCloud(controller.NewMetrics())
			_ = createNode(t, cloud, common.MultiResources{"CPU": []float64{0.0}}, nil, "testhostA")

			parent := createTask(t, cloud, &common.DbTask{
				DbId:   DbId(),
				Order:  0,
				Id:     "task_parent",
				Action: "wait",
				Finite: true,
			})
			child := createTask(t, cloud, &common.DbTask{
				DbId:      DbId(),
				Order:     0,
				Id:        "task_child",
				Action:    "run",
				Finite:    true,
				DependsOn: &parent.DbTask().Id,
			})

			cloud.HandleTasks()
			timeout := time.After(5 * time.Second)

			select {
			case taskCommand := <-cloud.TaskDispatchQueue:
				ASSERT(t, taskCommand.Task.Id == child.DbTask().Id, fmt.Sprintf("dispatched task %v instead of %v", taskCommand.Task.Id, child.DbTask().Id))
			case <-timeout:
				t.Fatalf("timed out")
			}

			select {
			case taskCommand := <-cloud.TaskDispatchQueue:
				t.Fatalf("dispatched task %v", taskCommand.Task.Id)
			default:
			}
		})
		t.Run("parent deleted", func(t *testing.T) {
			cloud := NewCloud(controller.NewMetrics())
			_ = createNode(t, cloud, common.MultiResources{"CPU": []float64{0.0}}, nil, "testhostA")

			dependsId := "task_depends"
			dependsPrio := 100
			childA := createTask(t, cloud, &common.DbTask{
				DbId:      DbId(),
				Order:     0,
				Priority:  &dependsPrio,
				Id:        "task_childA",
				Action:    "run",
				Finite:    true,
				DependsOn: &dependsId,
			})
			afterId := "task_after"
			afterPrio := 10
			childB := createTask(t, cloud, &common.DbTask{
				DbId:     DbId(),
				Order:    0,
				Priority: &afterPrio,
				Id:       "task_childB",
				Action:   "run",
				Finite:   true,
				After:    &afterId,
			})

			cloud.HandleTasks()
			timeout := time.After(5 * time.Second)

			select {
			case taskCommand := <-cloud.TaskDispatchQueue:
				ASSERT(t, taskCommand.Task.Id == childA.DbTask().Id, fmt.Sprintf("dispatched task %v instead of %v", taskCommand.Task.Id, childA.DbTask().Id))
			case <-timeout:
				t.Fatalf("timed out")
			}
			select {
			case taskCommand := <-cloud.TaskDispatchQueue:
				ASSERT(t, taskCommand.Task.Id == childB.DbTask().Id, fmt.Sprintf("dispatched task %v instead of %v", taskCommand.Task.Id, childB.DbTask().Id))
			case <-timeout:
				t.Fatalf("timed out")
			}
			select {
			case taskCommand := <-cloud.TaskDispatchQueue:
				t.Fatalf("dispatched task %v", taskCommand.Task.Id)
			default:
			}
		})
	})
	t.Run("task levels", func(t *testing.T) {
		cloud := NewCloud(controller.NewMetrics())
		locks := controller.NewLocks()
		// nodes
		nodeA := createNode(t, cloud, common.MultiResources{"CPU": []float64{100.0}}, &common.Flags{"test": false}, "testhostA")

		// parents for zero-level tasks
		parent1 := createTask(t, cloud, &common.DbTask{
			DbId:   DbId(),
			Order:  0,
			Id:     "task_parent1",
			Action: "run",
		})
		parent2 := createTask(t, cloud, &common.DbTask{
			DbId:   DbId(),
			Order:  0,
			Id:     "task_parent2",
			Action: "run",
		})

		// zero level
		altZero1 := "alt_zero1"
		_ = createTask(t, cloud, &common.DbTask{
			DbId:        DbId(),
			Id:          "task_level",
			Alternative: &altZero1,
			Order:       0,
			Level:       0,
			Action:      "run",
			After:       &parent1.DbTask().Id,
		})
		altZero2 := "alt_zero2"
		_ = createTask(t, cloud, &common.DbTask{
			DbId:        DbId(),
			Id:          "task_level",
			Alternative: &altZero2,
			Order:       1,
			Level:       0,
			Action:      "run",
			After:       &parent2.DbTask().Id,
		})

		// high level
		altHigh := "alt_high"
		taskHigh := createTask(t, cloud, &common.DbTask{
			DbId:        DbId(),
			Id:          "task_level",
			Alternative: &altHigh,
			Order:       2,
			Level:       42,
			Action:      "run",
		})

		task := cloud.GetTask("task_level", taskHigh.DbTask().DbId, locks)
		ASSERT(t, task != nil, "task not found")

		var output *bytes.Buffer
		var err error
		var ran bool

		// all lower-level tasks have a non-running parent => run higher-level task
		output = bytes.NewBuffer(nil)
		ran, err = cloud.HandleTask(task, output, locks)
		ASSERT(t, err == nil, "error: %s", err)
		ASSERT(t, ran, "higher-level task not dispatched (reason: %s)", output.Bytes())

		// at least one lower-order tasks has running parents => do NOT run higher-level task
		now := time.Now()
		start := now.Add(-30 * time.Second)
		dbNodeTask := common.DbNodeTask{
			Id:               "nodetask_parent1",
			Task:             parent1.DbTask().DbId,
			Node:             nodeA.Id,
			State:            "running",
			LastOnlineTime:   &now,
			LastRunningTime:  &now,
			StartTime:        &start,
			RunningSinceTime: &start,
			OriginTask: common.RunnableTask{
				DbTask: *parent1.DbTask(),
			},
		}
		ASSERT(t, !node.NeedsReservedResources(&dbNodeTask), "dbNodeTask should not have full resource reservation")
		cloud.AddNodeTask(&dbNodeTask, locks)

		output = bytes.NewBuffer(nil)
		ran, err = cloud.HandleTask(task, output, locks)
		ASSERT(t, err == nil, "error: %s", err)
		ASSERT(t, !ran, "higher-level task dispatched")

		altZero3 := "alt_zero3"
		_ = createTask(t, cloud, &common.DbTask{
			DbId:        DbId(),
			Id:          "task_level",
			Alternative: &altZero3,
			Order:       3,
			Level:       0,
			Action:      "run",
		})
		output = bytes.NewBuffer(nil)
		ran, err = cloud.HandleTask(task, output, locks)
		ASSERT(t, err == nil, "error: %s", err)
		ASSERT(t, !ran, "higher-level task dispatched")

		// all lower-level tasks have a non-running parent or have no parent=> run higher-level task
		dbNodeTask = common.DbNodeTask{
			Id:              "nodetask_parent1",
			Task:            parent1.DbTask().DbId,
			Node:            nodeA.Id,
			State:           "deleted",
			LastOnlineTime:  &now,
			LastRunningTime: &now,
			StartTime:       &start,
			OriginTask: common.RunnableTask{
				DbTask: *parent1.DbTask(),
			},
		}
		cloud.AddNodeTask(&dbNodeTask, locks)

		output = bytes.NewBuffer(nil)
		ran, err = cloud.HandleTask(task, output, locks)
		ASSERT(t, err == nil, "error: %s", err)
		ASSERT(t, ran, "higher-level task not dispatched (reason: %s)", output.Bytes())

		assertAllUnlocked(locks)
	})
	t.Run("parent with different level or revision", func(t *testing.T) {
		cloud := NewCloud(controller.NewMetrics())
		locks := controller.NewLocks()
		_ = createNode(t, cloud, nil, nil, "nodeX")

		parentRevision := createTask(t, cloud, &common.DbTask{
			DbId:     DbId(),
			Id:       "task_id_parent_revision",
			Revision: 42,
			Action:   "run",
		})
		taskRevision := createTask(t, cloud, &common.DbTask{
			DbId:      DbId(),
			Id:        "task_id_child_revision",
			Revision:  1337,
			Action:    "run",
			DependsOn: &parentRevision.DbTask().Id,
			After:     &parentRevision.DbTask().Id,
		})
		parentLevel := createTask(t, cloud, &common.DbTask{
			DbId:   DbId(),
			Id:     "task_id_parent_level",
			Level:  42,
			Action: "run",
		})
		taskLevel := createTask(t, cloud, &common.DbTask{
			DbId:      DbId(),
			Id:        "task_id_child_level",
			Level:     0,
			Action:    "run",
			DependsOn: &parentLevel.DbTask().Id,
			After:     &parentLevel.DbTask().Id,
		})

		var err error
		var ran bool

		ran, err = cloud.HandleTask(taskRevision, nil, locks)
		OK(t, err)
		ASSERT(t, !ran, "ran without a parent")
		ran, err = cloud.HandleTask(taskLevel, nil, locks)
		OK(t, err)
		ASSERT(t, !ran, "ran without a parent")

		now := time.Now()

		dbNodeTaskParentLevel := common.DbNodeTask{
			Id:               fmt.Sprintf("nodetask_%s", parentLevel.DbTask().Id),
			Task:             parentLevel.DbTask().DbId,
			Node:             "nodeX",
			State:            "running",
			LastOnlineTime:   &now,
			LastRunningTime:  &now,
			StartTime:        &now,
			RunningSinceTime: &now,
			OriginTask: common.RunnableTask{
				DbTask: *parentLevel.DbTask(),
			},
		}
		cloud.AddNodeTask(&dbNodeTaskParentLevel, locks)
		ran, err = cloud.HandleTask(taskLevel, nil, locks)
		OK(t, err)
		ASSERT(t, ran, "did not run")

		dbNodeTaskParentRevision := common.DbNodeTask{
			Id:               fmt.Sprintf("nodetask_%s", parentRevision.DbTask().Id),
			Task:             parentRevision.DbTask().DbId,
			Node:             "nodeX",
			State:            "running",
			LastOnlineTime:   &now,
			LastRunningTime:  &now,
			StartTime:        &now,
			RunningSinceTime: &now,
			OriginTask: common.RunnableTask{
				DbTask: *parentRevision.DbTask(),
			},
		}
		cloud.AddNodeTask(&dbNodeTaskParentRevision, locks)
		ran, err = cloud.HandleTask(taskRevision, nil, locks)
		OK(t, err)
		assertAllUnlocked(locks)
		ASSERT(t, ran, "did not run")
	})
	t.Run("limited attempts for finite tasks", func(t *testing.T) {
		if controller.FiniteTaskAttempts == 0 {
			t.Skip()
		}
		cloud := NewCloud(controller.NewMetrics())
		locks := controller.NewLocks()
		_ = createNode(t, cloud, nil, nil, "nodeX")

		taskA := createTask(t, cloud, &common.DbTask{
			DbId:     DbId(),
			Id:       "taskA",
			Action:   "run",
			Revision: 1,
			Finite:   true,
		})

		var ran bool
		var err error

		// without any attempts
		ran, err = cloud.HandleTask(taskA, nil, locks)
		OK(t, err)
		ASSERT(t, ran, "!ran")

		// the exact number of attempts as is the limit
		id := fmt.Sprintf("nodetask_%s", taskA.DbTask().Id)
		for i := 0; i < controller.FiniteTaskAttempts; i++ {
			// still attempts remaining
			ran, err = cloud.HandleTask(taskA, nil, locks)
			OK(t, err)
			ASSERT(t, ran, "!ran")

			// create a starting instance
			now := time.Now()
			dbNodeTask := common.DbNodeTask{
				Id:             id,
				Task:           taskA.DbTask().DbId,
				Node:           "nodeX",
				State:          "starting",
				LastOnlineTime: &now,
				StartTime:      &now,
				OriginTask: common.RunnableTask{
					DbTask: *taskA.DbTask(),
				},
			}
			cloud.AddNodeTask(&dbNodeTask, locks)
		}

		// out of limit attempt
		ran, err = cloud.HandleTask(taskA, nil, locks)
		OK(t, err)
		ASSERT(t, !ran, "ran more times than the limit")

		// new revision should reset the counter and allow more attempts
		taskA = createTask(t, cloud, &common.DbTask{
			DbId:     taskA.DbTask().DbId,
			Id:       taskA.DbTask().Id,
			Action:   "run",
			Revision: taskA.DbTask().Revision + 1,
			Finite:   true,
		})
		ran, err = cloud.HandleTask(taskA, nil, locks)
		OK(t, err)
		assertAllUnlocked(locks)
		ASSERT(t, ran, "!ran")
	})
}

func createController(tb testing.TB, cloud *Cloud, hostname string) *Controller {
	tb.Helper()

	now := time.Now()
	controllerTmp := NewController(common.DbController{
		Id:         "controller_" + hostname, //TODO use a function
		Host:       hostname,
		LastOnline: now,
		State_:     "idle",
	})
	controllerJson, err := json.Marshal(controllerTmp)
	OK(tb, err)

	dbController, err := common.NewDbController(controllerJson)
	OK(tb, err)
	ASSERT(tb, dbController != nil, "NewDbController returned nil DbController without an error")

	_ = cloud.AddController(dbController)
	/*if !added {
		tb.Fatalf("AddController returned false on a new controller addition")
	}*/

	controller := cloud.GetController(dbController.Id)
	ASSERT(tb, controller != nil, "cloud.GetController returned nil Controller")

	return controller
}

func createNode(tb testing.TB, cloud *Cloud, freeResources common.MultiResources, capabilities *common.Flags, hostname string) *node.Node {
	tb.Helper()

	now := time.Now()
	nodeTmp := node.NewNode(common.DbNode{
		Id:            "node_" + hostname, //TODO use a function
		Host:          hostname,
		FreeResources: freeResources,
		LastOnline:    &now,
		State:         "running",
	})
	nodeJson, err := json.Marshal(nodeTmp)
	OK(tb, err)

	locks := controller.NewLocks()
	dbNode, err := NewDbNode(nodeJson)
	OK(tb, err)
	ASSERT(tb, dbNode != nil, "NewDbNode returned nil DbNode without an error")

	_, _ = cloud.AddNode(dbNode, locks)
	/*if !added {
		tb.Fatalf("AddNode returned false on a new node addition")
	}*/

	// update the Node so it's enabled
	dbNode2 := common.DbNode{
		Id:           "node_" + hostname,
		Enabled:      true,
		Capabilities: capabilities,
	}
	cloud.UpdateNode(&dbNode2, locks)

	node := cloud.GetNode(dbNode.Id, locks)
	assertAllUnlocked(locks)
	ASSERT(tb, node != nil, "cloud.GetNode returned nil Node")

	return node
}

func createTask(tb testing.TB, cloud *Cloud, dbTask *common.DbTask) *task.Task {
	tb.Helper()
	locks := controller.NewLocks()

	task := cloud.AddTask(dbTask, false, locks)
	ASSERT(tb, task != nil, "cloud.AddTask returned nil Task")

	return task
}
