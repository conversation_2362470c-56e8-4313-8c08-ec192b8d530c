package cloud

import (
	"fmt"
	"testing"
	"time"

	. "git.moderntv.eu/go/test"
	"git.moderntv.eu/mcloud/log"
	"git.moderntv.eu/mcloud/system/common"
	"git.moderntv.eu/mcloud/system/controller"
	"git.moderntv.eu/mcloud/system/controller/node"
	"git.moderntv.eu/mcloud/system/controller/task"
)

func init() {
	log.SetLogger(&log.NullLogger{})
}

func TestStructures(t *testing.T) {
	const N = 100

	t.Run("add and remove Controller", func(t *testing.T) {
		cloud := NewCloud(controller.NewMetrics())
		// locks := controller.NewLocks()

		list := make(map[string]struct{})
		for i := 0; i < N; i++ {
			hostname := fmt.Sprintf("host%d", i)
			controller := createController(t, cloud, hostname)
			list[controller.Id] = struct{}{}

		}

		for id, _ := range list {
			controller := cloud.GetController(id)
			ASSERT(t, controller != nil, "controller with id %s not found", id)
			cloud.RemoveController(controller)
		}

		ASSERT(t, len(cloud.controllers) == 0, "not all controllers were deleted")
	})
	t.Run("add and remove Task", func(t *testing.T) {
		cloud := NewCloud(controller.NewMetrics())
		locks := controller.NewLocks()

		taskIds := make(map[string]struct{})
		dbTasks := make(map[string]*common.DbTask)

		for i := 0; i < N; i++ {
			prio := i % 32
			id := fmt.Sprintf("task_id_%d", i)
			taskIds[id] = struct{}{}

			dbTask := common.DbTask{
				DbId:     i,
				Id:       id,
				Action:   "run",
				Priority: &prio,
			}
			dbTasks[id] = &dbTask

			_ = createTask(t, cloud, &dbTask)
		}

		numTasks := len(taskIds)

		for task := range cloud.TaskIterator(func(task *task.Task) bool { return true }, locks) {
			delete(taskIds, task.DbTask().Id)
		}

		ASSERT(t, len(taskIds) == 0, "not all Tasks were added")
		ASSERT(t, cloud.taskList.Len() == N, "not all task were added to TaskList")

		/*t.Run("not removing when not in an appropriate Action", func(t *testing.T) {
			for _, dbTask := range dbTasks {
				cloud.RemoveTask(dbTask)
			}

			found := 0
			for _ = range cloud.TaskIterator(func(task *Task) bool { return true }) {
				found++
			}

			ASSERT(t, found == numTasks, "some Tasks were deleted even with Action = 'run'")
			ASSERT(t, cloud.taskList.Len() == N, "tasks missing from TaskList")
		})*/
		t.Run("not removing when non-zero nodeTasks", func(t *testing.T) {
			dbNodeTasks := make(map[string]*common.DbNodeTask)
			for _, dbTask := range dbTasks {
				cloud.TerminateTask(dbTask, locks)

				idn := fmt.Sprintf("nodetask_of_%s_on_%s", dbTask.Id, "node_id_test")
				dbNodeTask := common.DbNodeTask{
					Id:    idn,
					Task:  dbTask.DbId,
					Node:  "node_id_test",
					State: "running",
					OriginTask: common.RunnableTask{
						DbTask: *dbTask,
					},
				}
				dbNodeTasks[idn] = &dbNodeTask

				cloud.AddNodeTask(&dbNodeTask, locks)
				cloud.RemoveTask(dbTask, locks)
			}

			found := 0
			for range cloud.TaskIterator(func(task *task.Task) bool { return true }, locks) {
				found++
			}
			ASSERT(t, found == numTasks, "some Tasks were deleted even when they had running NodeTasks")

			for _, dbNodeTask := range dbNodeTasks {
				cloud.RemoveNodeTask(dbNodeTask, locks)
			}
			for task := range cloud.TaskIterator(func(task *task.Task) bool { return true }, locks) {
				ASSERT(t, task.NodeTasksLen() == 0, "not all nodeTasks were deleted")
			}
		})
		t.Run("deletion", func(t *testing.T) {
			for _, dbTask := range dbTasks {
				cloud.RemoveTask(dbTask, locks)
			}

			for _ = range cloud.TaskIterator(func(task *task.Task) bool { return true }, locks) {
				t.Fatalf("some Tasks were not deleted")
			}
		})
	})
	t.Run("add and remove NodeTask", func(t *testing.T) {
		cloud := NewCloud(controller.NewMetrics())
		locks := controller.NewLocks()

		nodes := make([]*node.Node, 2)
		nodes[0] = createNode(t, cloud, common.MultiResources{"CPU": []float64{100.0}}, nil, "testhost1")
		nodes[1] = createNode(t, cloud, common.MultiResources{"CPU": []float64{100.0}}, nil, "testhost2")

		list := make(map[string]*common.DbNodeTask)
		for i := 0; i < N; i++ {
			id := fmt.Sprintf("task_id_%d", i)
			dbTask := common.DbTask{
				DbId:   i,
				Id:     id,
				Action: "run",
			}
			_ = createTask(t, cloud, &dbTask)

			now := time.Now()
			start := now.Add(-1 * time.Hour)
			for j := range nodes {
				idn := fmt.Sprintf("nodetask_of_%d_on_%s", i, nodes[j].Id)
				dbNodeTask := common.DbNodeTask{
					Id:               idn,
					Task:             i,
					Node:             nodes[j].Id,
					State:            "running",
					LastOnlineTime:   &now,
					LastRunningTime:  &now,
					StartTime:        &start,
					RunningSinceTime: &start,
					OriginTask: common.RunnableTask{
						DbTask: dbTask,
					},
				}
				ASSERT(t, !node.NeedsReservedResources(&dbNodeTask), "dbNodeTask should not have full resource reservation")
				cloud.AddNodeTask(&dbNodeTask, locks)
				list[idn] = &dbNodeTask
			}
			ASSERT(t, cloud.IsTaskIdRunning(dbTask.Id, 0, locks), "task not reported as running")
		}

		for _, node := range nodes {
			num := node.NodeNumNodeTasks(locks)
			ASSERT(t, num == N, "node %s has %v NodeTasks, expected %v", node.Id, num, N)
		}
		for task := range cloud.TaskIterator(func(task *task.Task) bool { return true }, locks) {
			ASSERT(t, len(task.GetNodeTaskList(locks)) == 2, "task has invalid number of NodeTasks: %d", task.NodeTasksLen())
		}

		for _, dbNodeTask := range list {
			cloud.RemoveNodeTask(dbNodeTask, locks)
		}

		for _, node := range nodes {
			num := node.NodeNumNodeTasks(locks)
			ASSERT(t, num == 0, "not all NodeTasks were deleted from their nodes list")
		}

		for task := range cloud.TaskIterator(func(task *task.Task) bool { return true }, locks) {
			ASSERT(t, task.NodeTasksLen() == 0, "not all NodeTasks were deleted from their tasks list")
			ASSERT(t, !task.HasRunningNodeTasks(), "task has zero running NodeTask but HasRunningNodeTasks function reports otherwise")
		}
	})
	t.Run("add and remove Node", func(t *testing.T) {
		cloud := NewCloud(controller.NewMetrics())
		locks := controller.NewLocks()

		list := make(map[string]struct{})
		for i := 0; i < N; i++ {
			hostname := fmt.Sprintf("host%d", i)
			node := createNode(t, cloud, common.MultiResources{"CPU": []float64{float64(i % 32)}}, nil, hostname)
			list[node.Id] = struct{}{}
		}

		ASSERT(t, len(cloud.nodeLists) == 1, "nodeList for resource not added")
		ASSERT(t, cloud.nodeLists["CPU"].Len() == N, "nodes not added to a nodeList")

		for id, _ := range list {
			node := cloud.GetNode(id, locks)
			ASSERT(t, node != nil, "node with id %s not found", id)
			cloud.RemoveNode(node, locks)
		}

		cloud.nodesMu.RLock()
		defer cloud.nodesMu.RUnlock()

		ASSERT(t, len(cloud.nodes) == 0, "not all nodes were deleted")
		for _, nodeList := range cloud.nodeLists {
			ASSERT(t, nodeList.Len() == 0, "not all nodes were removed from NodeList")
		}
	})
	t.Run("add Node and Task through new NodeTask", func(t *testing.T) {
		cloud := NewCloud(controller.NewMetrics())
		locks := controller.NewLocks()

		dbTask := common.DbTask{
			DbId:   0,
			Id:     "task_id_test",
			Action: "run",
		}

		now := time.Now()
		start := now.Add(-1 * time.Hour)
		dbNodeTask := common.DbNodeTask{
			Id:               "nodetask_id_test",
			Task:             dbTask.DbId,
			Node:             "node_id_test",
			State:            "running",
			LastOnlineTime:   &now,
			LastRunningTime:  &now,
			StartTime:        &start,
			RunningSinceTime: &start,
			OriginTask: common.RunnableTask{
				DbTask: dbTask,
			},
		}
		ASSERT(t, !node.NeedsReservedResources(&dbNodeTask), "dbNodeTask should not have full resource reservation")
		cloud.AddNodeTask(&dbNodeTask, locks)

		tasks := 0
		for task := range cloud.TaskIterator(func(task *task.Task) bool { return true }, locks) {
			tasks++

			ASSERT(t, task.NodeTasksLen() == 1, fmt.Sprintf("task has invalid number of NodeTasks: %d", task.NodeTasksLen()))
			ASSERT(t, task.HasRunningNodeTasks(), "task has running NodeTask but HasRunningNodeTasks function reports otherwise")
		}
		ASSERT(t, tasks == 1, "found %d tasks instead of 1", tasks)
		node := cloud.GetNode("node_id_test", locks)
		ASSERT(t, node != nil, "Node not found")
		ASSERT(t, node.Host == "<zombie>", "Node is not a zombie node")
		for _, nodeList := range cloud.nodeLists {
			ASSERT(t, nodeList.Len() == 0, "zombie node was added to a NodeList")
		}

		// a <zombie> node should be replaced by regular node when the node becomes online
		_ = createNode(t, cloud, common.MultiResources{"X": []float64{50.0}}, nil, "id_test")
		node = cloud.GetNode("node_id_test", locks)
		ASSERT(t, node != nil, "Node not found after it was added")
		ASSERT(t, node.Host != "<zombie>", "Node is a zombie node when it shouldn't")
		ASSERT(t, len(cloud.nodeLists) == 1, "nodeList for resource not added")
		ASSERT(t, cloud.nodeLists["X"].Len() == 1, "node was not added to the NodeList after it was added")
	})
	t.Run("A <zombie> node should be removed when last of its NodeTasks is removed", func(t *testing.T) {
		t.Skip("test not implemented")
		//TODO
	})
	t.Run("same NodeTasks should not count as multiple starting attemptst", func(t *testing.T) {
		cloud := NewCloud(controller.NewMetrics())
		locks := controller.NewLocks()

		dbTask := common.DbTask{
			DbId:   1,
			Id:     "taskA",
			Action: "run",
		}
		taskA := createTask(t, cloud, &dbTask)
		ASSERT(t, taskA.StartedCounter.Load() == 0, "bad counter")

		start := time.Now().Add(-1 * time.Hour)
		nodeTaskId := "nodetaskA"
		for i := 0; i < 3; i++ {
			now := time.Now()
			dbNodeTask := common.DbNodeTask{
				Id:             nodeTaskId,
				Task:           dbTask.DbId,
				Node:           "nodeX",
				State:          "starting",
				LastOnlineTime: &now,
				StartTime:      &start,
				OriginTask: common.RunnableTask{
					DbTask: dbTask,
				},
			}
			cloud.AddNodeTask(&dbNodeTask, locks)
			ASSERT(t, taskA.StartedCounter.Load() == 1, "bad counter: %d", taskA.StartedCounter)
		}
	})
	t.Run("TaskList operations", func(t *testing.T) {
		const Mod = 16
		cloud := NewCloud(controller.NewMetrics())
		locks := controller.NewLocks()

		dbTasksIn := make([]*common.DbTask, 0, N*2)
		dbTasksOut := make([]*common.DbTask, 0, N*2)
		dbNodeTasks := make(map[string]*common.DbNodeTask)

		t.Run("add Tasks", func(t *testing.T) {
			for i := 0; i < N*2; i++ {
				prio := i % 32
				alternative := fmt.Sprintf("alternative_%d", prio)
				action := "run"
				if i%2 == 0 {
					action = "terminate"
				}
				dbTask := common.DbTask{
					DbId:        i,
					Id:          fmt.Sprintf("task_id_%d", i%Mod),
					Alternative: &alternative,
					Action:      action,
					Priority:    &prio,
				}
				if action == "run" {
					dbTasksIn = append(dbTasksIn, &dbTask)
				} else {
					dbTasksOut = append(dbTasksOut, &dbTask)
				}
				_ = createTask(t, cloud, &dbTask)

				ASSERT(t, cloud.taskList.Len() == len(dbTasksIn), fmt.Sprintf("TaskList size mismatch (%d != %d)", cloud.taskList.Len(), len(dbTasksIn)))
			}
		})
		t.Run("add NodeTasks", func(t *testing.T) {
			var i int

			i = 0
			for _, dbTask := range dbTasksIn {
				idn := fmt.Sprintf("nodetask_of_%d_on_%s", dbTask.DbId, "node_id_test")
				dbNodeTask := common.DbNodeTask{
					Id:    idn,
					Task:  dbTask.DbId,
					Node:  "node_id_test",
					State: "running",
					OriginTask: common.RunnableTask{
						DbTask: *dbTask,
					},
				}
				dbNodeTasks[idn] = &dbNodeTask
				cloud.AddNodeTask(&dbNodeTask, locks)

				i++
				if i == Mod {
					break
				}
			}
			ASSERT(t, cloud.taskList.Len() == N-Mod, "TaskList size mismatch")

			i = 0
			for _, dbTask := range dbTasksOut {
				idn := fmt.Sprintf("nodetask_of_%d_on_%s", dbTask.DbId, "node_id_test")
				dbNodeTask := common.DbNodeTask{
					Id:    idn,
					Task:  dbTask.DbId,
					Node:  "node_id_test",
					State: "failed",
					OriginTask: common.RunnableTask{
						DbTask: *dbTask,
					},
				}
				dbNodeTasks[idn] = &dbNodeTask
				cloud.AddNodeTask(&dbNodeTask, locks)

				i++
				if i == Mod {
					break
				}
			}
			ASSERT(t, cloud.taskList.Len() == N, fmt.Sprintf("TaskList size mismatch (%d != %d)", cloud.taskList.Len(), N))
		})
		t.Run("change action of Tasks", func(t *testing.T) {
			for _, dbTask := range dbTasksIn {
				dbTask.Action = "terminate"
				cloud.AddTask(dbTask, false, locks)
			}
			for _, dbTask := range dbTasksOut {
				dbTask.Action = "run"
				cloud.AddTask(dbTask, false, locks)
			}
			ASSERT(t, cloud.taskList.Len() == N, fmt.Sprintf("TaskList size mismatch (%d != %d)", cloud.taskList.Len(), N))
		})
		t.Run("change revision of Tasks", func(t *testing.T) {
			for _, dbTask := range dbTasksOut {
				dbTask.Revision += 1
				cloud.AddTask(dbTask, false, locks)
				break
			}
			ASSERT(t, cloud.taskList.Len() == N+1, fmt.Sprintf("TaskList size mismatch (%d != %d)", cloud.taskList.Len(), N+1))
			for _, dbTask := range dbTasksIn {
				dbTask.Revision += 1
				cloud.AddTask(dbTask, false, locks)
				break
			}
			ASSERT(t, cloud.taskList.Len() == N+1, fmt.Sprintf("TaskList size mismatch (%d != %d)", cloud.taskList.Len(), N+1))
		})
		t.Run("remove NodeTasks", func(t *testing.T) {
			for _, dbNodeTask := range dbNodeTasks {
				cloud.RemoveNodeTask(dbNodeTask, locks)
			}
			ASSERT(t, cloud.taskList.Len() == len(dbTasksOut), "TaskList size mismatch (%d != %d)", cloud.taskList.Len(), len(dbTasksOut))
		})
		t.Run("remove Tasks", func(t *testing.T) {
			for _, dbTask := range dbTasksIn {
				cloud.RemoveTask(dbTask, locks)
			}
			ASSERT(t, cloud.taskList.Len() == len(dbTasksOut), "TaskList size mismatch")
			for _, dbTask := range dbTasksOut {
				dbTask.Action = "wait"
				cloud.AddTask(dbTask, false, locks)
			}
			for _, dbTask := range dbTasksOut {
				cloud.RemoveTask(dbTask, locks)
			}
			ASSERT(t, cloud.taskList.Len() == 0, fmt.Sprintf("TaskList not empty after deleting tasks (%d)", cloud.taskList.Len()))
		})
	})
}

/*
// The test is takes too long. Uncomment after modification.
func TestTimers(t *testing.T) {
	if testing.Short() {
		t.Skip("skipping testing of timers in short mode")
	}
	t.Run("Node removal timer", func(t *testing.T) {
		t.Parallel()

		cloud := NewCloud()

		node := createNode(t, cloud, common.MultiResources{"CPU": []float64{100.0}}, nil, "test")

		dbTask := common.DbTask{
			DbId:   0,
			Id:     "task_test",
			Action: "run",
		}
		now := time.Now()
		start := now.Add(-1 * time.Hour)
		dbNodeTask := common.DbNodeTask{
			Id:               "nodetask_test",
			Task:             dbTask.DbId,
			Node:             "node_test",
			State:            "running",
			LastOnlineTime:   &now,
			LastRunningTime:  &now,
			StartTime:        &start,
			RunningSinceTime: &start,
			OriginTask:       common.RunnableTask{DbTask: dbTask},
		}
		ASSERT(t, !NeedsReservedResources(&dbNodeTask), "dbNodeTask should not have full resource reservation")
		cloud.AddNodeTask(&dbNodeTask)

		// wait until the Nodes remove timer is fired
		time.Sleep(NodeRemoveTimeout + 1*time.Second)

		// Node was removed
		node = cloud.GetNode(node.Id)
		ASSERT(t, node == nil, "node not deleted")

		// readd Node and NodeTask
		node = createNode(t, cloud, common.MultiResources{"CPU": []float64{100.0}}, nil, "test")
		now = time.Now()
		cloud.AddNodeTask(&dbNodeTask)

		// NodeTask is again accessible through the Node
		nodes := cloud.GetNodes()
		var found bool
		for _, node := range nodes {
			nodeTasks := node.GetNodeTaskList()
			if node.Host == "<zombie>" {
				continue
			}
			if len(nodeTasks) == 0 {
				continue
			}
			ASSERT(t, len(nodeTasks) == 1, "node has more than 1 NodeTask")
			ASSERT(t, nodeTasks[0].Id == dbNodeTask.Id, fmt.Sprintf("node has wrong NodeTask: %s", nodeTasks[0].Id))
			found = true
		}
		ASSERT(t, found, "NodeTask not found")

	})
	t.Run("Node turn into <zombie> if there are NodeTasks after removal", func(t *testing.T) {
		t.Skip("feature not implemented")
		//TODO
		t.Parallel()

		cloud := NewCloud()

		node := createNode(t, cloud, common.MultiResources{"CPU": []float64{100.0}}, nil, "testhost1")
		dbTask := common.DbTask{
			DbId:   0,
			Id:     "task_id_test",
			Action: "run",
		}

		// keep the NodeTask
		start := time.Now().Add(-1 * time.Hour)
		ticker := time.NewTicker(NodeTaskRemoveTimeout / 2)
		go func() {
			for {
				now := time.Now()
				dbNodeTask := common.DbNodeTask{
					Id:               "nodetask_id_test",
					Task:             dbTask.DbId,
					Node:             node.Id,
					State:            "running",
					LastOnlineTime:   &now,
					LastRunningTime:  &now,
					StartTime:        &start,
					RunningSinceTime: &start,
					OriginTask: common.RunnableTask{
						DbTask: dbTask},
				}
				ASSERT(t, !NeedsReservedResources(&dbNodeTask), "dbNodeTask should not have full resource reservation")

				cloud.AddNodeTask(&dbNodeTask)
				<-ticker.C
			}
		}()
		defer ticker.Stop()

		// wait until the Nodes remove timer is fired
		time.Sleep(NodeRemoveTimeout + 1*time.Second)

		// check that the node is a <zombie> node
		node = cloud.GetNode(node.Id)
		ASSERT(t, node != nil, "Node not found")
		ASSERT(t, node.Host == "<zombie>", "Node is not a zombie node")
	})
	t.Run("NodeTask removal timer", func(t *testing.T) {
		t.Parallel()

		cloud := NewCloud()

		dbTask := common.DbTask{
			DbId:   0,
			Id:     "task_id_test",
			Action: "run",
		}

		now := time.Now()
		start := now.Add(-1 * time.Hour)
		dbNodeTask := common.DbNodeTask{
			Id:               "nodetask_id_test",
			Task:             dbTask.DbId,
			Node:             "node_id_test",
			State:            "running",
			LastOnlineTime:   &now,
			LastRunningTime:  &now,
			StartTime:        &start,
			RunningSinceTime: &start,
			OriginTask: common.RunnableTask{
				DbTask: dbTask,
			},
		}
		ASSERT(t, !NeedsReservedResources(&dbNodeTask), "dbNodeTask should not have full resource reservation")
		cloud.AddNodeTask(&dbNodeTask)

		tasks := 0
		var task *Task
		for task = range cloud.TaskIterator(func(task *Task) bool { return true }) {
			tasks++
			task.mu.RLock()
			ASSERT(t, len(task.nodeTasks) == 1, fmt.Sprintf("task has invalid number of NodeTasks: %d", len(task.nodeTasks)))
			task.mu.RUnlock()
		}
		ASSERT(t, tasks == 1, fmt.Sprintf("found %d tasks instead of 1", tasks))

		cloud.mu.RLock()
		ASSERT(t, cloud.taskList.Len() == 0, fmt.Sprintf("TaskList size mismatch (%d != %d)", cloud.taskList.Len(), 1))
		cloud.mu.RUnlock()

		// wait until the NodeTask remove timer is fired
		time.Sleep(NodeTaskRemoveTimeout + 1*time.Second)

		// check current NodeTasks
		node := cloud.GetNode("node_id_test")
		ASSERT(t, node != nil, "node not found")
		ASSERT(t, len(node.nodeTasks) == 0, "node has non-zero nodeTasks")

		cloud.mu.RLock()
		ASSERT(t, cloud.taskList.Len() == 1, fmt.Sprintf("TaskList size mismatch (%d != %d)", cloud.taskList.Len(), 1))
		cloud.mu.RUnlock()

		tasks = 0
		for task = range cloud.TaskIterator(func(task *Task) bool { return true }) {
			tasks++
			ASSERT(t, len(task.nodeTasks) == 0, fmt.Sprintf("task has invalid number of NodeTasks: %d", len(task.nodeTasks)))
		}
		ASSERT(t, tasks == 1, fmt.Sprintf("found %d tasks instead of 1", tasks))
	})
}
*/

func BenchmarkNodeOperations(b *testing.B) {
	const NumNodes = 1000
	b.Run(fmt.Sprintf("creation and deletion of %d nodes", NumNodes), func(b *testing.B) {
		for n := 0; n < b.N; n++ {
			cloud := NewCloud(controller.NewMetrics())
			locks := controller.NewLocks()
			var nodes []*node.Node
			for i := 0; i < NumNodes; i++ {
				nodes = append(nodes, createNode(b, cloud, common.MultiResources{"CPU": []float64{float64(i) * 10.0}}, nil, fmt.Sprintf("testhost_%d", i)))
			}
			for _, node := range nodes {
				cloud.RemoveNode(node, locks)
			}
		}
	})
}

func BenchmarkTasks(b *testing.B) {
	const NumNodes = 100
	const NumTasks = 100000
	b.Run(fmt.Sprintf("%d tasks", NumTasks), func(b *testing.B) {
		for n := 0; n < b.N; n++ {
			cloud := NewCloud(controller.NewMetrics())
			locks := controller.NewLocks()

			var nodes []*node.Node
			for i := 0; i < NumNodes; i++ {
				nodes = append(nodes, createNode(b, cloud, common.MultiResources{"CPU": []float64{float64(i) * 10.0}}, nil, fmt.Sprintf("testhost_%d", i)))
			}

			for i := 0; i < NumTasks; i++ {
				node := nodes[i%NumNodes]

				taskId := fmt.Sprintf("task_id_%d", i)
				dbTask := common.DbTask{
					DbId:   i,
					Id:     taskId,
					Action: "run",
				}
				_ = createTask(b, cloud, &dbTask)

				now := time.Now()
				start := now.Add(-1 * time.Hour)
				idn := fmt.Sprintf("nodetask_of_%s_on_%s", dbTask.Id, node.Id)
				dbNodeTask := common.DbNodeTask{
					Id:               idn,
					Task:             dbTask.DbId,
					Node:             node.Id,
					State:            "running",
					LastOnlineTime:   &now,
					LastRunningTime:  &now,
					StartTime:        &start,
					RunningSinceTime: &start,
					OriginTask: common.RunnableTask{
						DbTask: dbTask,
					},
				}
				cloud.AddNodeTask(&dbNodeTask, locks)
			}

			for i := 0; i < NumTasks; i++ {
				node := nodes[i%NumNodes]
				taskId := fmt.Sprintf("task_id_%d", i)
				nodeTaskId := fmt.Sprintf("nodetask_of_%s_on_%s", taskId, node.Id)
				dbNodeTask := common.DbNodeTask{
					Id:   nodeTaskId,
					Task: i,
					Node: node.Id,
				}
				cloud.RemoveNodeTask(&dbNodeTask, locks)
			}

			for _, node := range nodes {
				cloud.RemoveNode(node, locks)
			}
		}
	})
}
