package cloud

import (
	"fmt"
	"io"
	"math"
	"math/rand"
	"sort"
	"time"

	gocommon "git.moderntv.eu/go/common"
	"git.moderntv.eu/mcloud/log"
	"git.moderntv.eu/mcloud/system/common"
	"git.moderntv.eu/mcloud/system/controller"
	"git.moderntv.eu/mcloud/system/controller/node"
	"git.moderntv.eu/mcloud/system/controller/task"
)

var (
	ErrNoSatNodes = fmt.Errorf("no nodes satisfying needs of the task")
)

type Candidate struct {
	Node              *node.Node
	SelectedResources common.SelectedResources
}

func (cloud *Cloud) CheckTaskForNode(task *task.Task, node *node.Node, preferredResource string, locks controller.Locks) (common.SelectedResources, error) {
	err := node.CanRunTasks(locks)
	if err != nil {
		return nil, err
	}

	dbTask := task.DbTask()
	err = node.CheckRequirements(dbTask.Requirements, locks)
	if err != nil {
		return nil, err
	}

	selectedResources, err := SelectResources(task, node, preferredResource, locks)
	if err != nil {
		return nil, err
	}
	/*	if len(task.nodeTasks) != 0 {
		for _, nodeTask := range task.nodeTasks {
			if nodeTask.Node == dbNode.Id {
				LogDebug("Node '%v' has already running Task '%s'", dbNode.Id, task)
				return false
			}
		}
	}*/

	return selectedResources, nil
}

func (cloud *Cloud) TestTaskOnNode(task *task.Task, candidate *Candidate, candidates []Candidate, selectedResource string, fn func(*task.Task, []Candidate, string, controller.Locks) bool, locks controller.Locks) (interface{}, error) {
	var dummyDbTask = *task.DbTask()
	var dummyAlternative = "dummy"
	dummyDbTask.Alternative = &dummyAlternative
	dummyDbTask.DbId = -1

	runnableTask := common.RunnableTask{
		DbTask:            dummyDbTask,
		SelectedResources: candidate.SelectedResources,
	}
	var dummy = cloud.AddDummyNodeTask(runnableTask, candidate.Node, locks)

	result := fn(task, candidates, selectedResource, locks)

	cloud.removeDummyNodeTask(dummy, locks)

	return result, nil
}

func testResourceBalance(task *task.Task, candidates []Candidate, selectedResource string, locks controller.Locks) bool {
	var min float64 = math.MaxFloat64
	var max float64 = -1
	for _, c := range candidates {
		avail_, _ := c.Node.GetAvailableResource(selectedResource, locks)
		for _, avail := range avail_ {
			if avail < min {
				min = avail
			}
			if avail > max {
				max = avail
			}
		}
	}

	return min >= max*0.7
}

/*func (self *Cloud) TestResourceBalance(task *Task, selectedResources SelectedResources) bool {
	var dbTask = task.DbTask()
	for resource, _ := range *dbTask.RequiredResources {
		var min float64 = math.MaxFloat64
		var max float64 = -1
		for _, node := range self.nodes {
			avail_ := node.GetAvailableResource(resource)
			for _, avail := range avail_ {
				if avail < min {
					min = avail
				}
				if avail > max {
					max = avail
				}
			}
		}
		if min < max*0.7 {
			return false
		}
	}

	return true
}*/

func (cloud *Cloud) SelectNode(task *task.Task, debugw io.Writer, locks controller.Locks) (*node.Node, common.SelectedResources, error) {
	var dbTask = task.DbTask()

	var selectedResource string
	if dbTask.RequiredResources != nil {
		globalFree := make(map[string]float64)
		for resource := range *dbTask.RequiredResources {
			locks.RLockNodes()
			cloud.nodesMu.RLock()
			for _, node := range cloud.nodes { //TODO do without a loop over all nodes
				ok := node.HasFreeResource(resource, locks)
				if !ok {
					continue
				}

				avail, _ := node.GetAvailableResource(resource, locks)
				for _, a := range avail {
					globalFree[resource] += a
				}
			}
			locks.RUnlockNodes()
			cloud.nodesMu.RUnlock()
		}

		var minRatio float64
		for res, val := range *dbTask.RequiredResources {
			if val == 0 {
				continue
			}

			ratio := globalFree[res] / val
			if selectedResource == "" || ratio < minRatio {
				selectedResource = res
				minRatio = ratio
			}
		}
	}
	if selectedResource == "" {
		selectedResource = "CPU"
	}
	if debugw != nil {
		fmt.Fprintf(debugw, "- nodes ordered by: %s\n", selectedResource)
	}

	var nodes []*node.Node
	locks.RLockNodes()
	cloud.nodesMu.RLock()
	nodeList := cloud.nodeLists[selectedResource]
	if nodeList == nil {
		for _, node := range cloud.nodes {
			nodes = append(nodes, node)
		}
	} else {
		controller.AssertLock(locks.Nodes, controller.LockRLocked)
		nodes = nodeList.Copy()
	}
	locks.RUnlockNodes()
	cloud.nodesMu.RUnlock()
	if len(nodes) == 0 {
		return nil, nil, fmt.Errorf("no nodes online")
	}

	var counter int
	var err error

	var candidates []Candidate
	if debugw != nil {
		fmt.Fprintf(debugw, "- selecting candidate nodes:\n")
	}
	for _, node := range nodes {
		if debugw != nil {
			counter++
			fmt.Fprintf(debugw, "  %02d) node '%s'\n", counter, node.Id)
		}
		var selectedResources common.SelectedResources
		selectedResources, err = cloud.CheckTaskForNode(task, node, selectedResource, locks)
		if err != nil {
			if debugw != nil {
				fmt.Fprintf(debugw, "      not selected: %s\n", err)
			}
			continue
		}
		if debugw != nil {
			fmt.Fprintf(debugw, "      SELECTED\n")
		}
		candidates = append(candidates, Candidate{
			Node:              node,
			SelectedResources: selectedResources,
		})
	}

	var parentGroup string
	var childrenGroup string

	// check if there is a node where all the children are running
	// TODO replace with a list with nodes ordered by the number of children
	var nodeWithChildren *node.Node
	var conflict bool
	if controller.PreferNodesWithChildren {
		for _, childTask := range task.Children(locks) {
			childTaskNodeTasks := childTask.GetNodeTaskList(locks)
			for _, nodeTask := range childTaskNodeTasks {
				var dbNodeTask = nodeTask.DbNodeTask()
				if dbNodeTask.State != "running" {
					continue
				} //TODO check revision of the child?
				if nodeWithChildren != nil && nodeWithChildren.Id != dbNodeTask.Node {
					conflict = true
					nodeWithChildren = nil
				}
				if !conflict {
					nodeWithChildren = cloud.GetNode(dbNodeTask.Node, locks)
				}
			}
		}
	}
	if nodeWithChildren != nil {
		childrenGroup = nodeWithChildren.Group

		// check if the node is a candidate
		var candidate *Candidate
		for i, c := range candidates {
			if c.Node.Id == nodeWithChildren.Id {
				candidate = &candidates[i]
				break
			}
		}
		if candidate != nil {
			// discard decision if it would make resources unbalanced between nodes
			// TODO only consider candidate nodes
			balanced, err := cloud.TestTaskOnNode(task, candidate, candidates, selectedResource, testResourceBalance, locks)
			if err == nil && balanced.(bool) {
				if debugw != nil {
					fmt.Fprintf(debugw, "- using node with children\n")
				}
				return candidate.Node, candidate.SelectedResources, nil
			} else {
				if debugw != nil {
					fmt.Fprintf(debugw, "- not preferring node with children: resource imbalance\n")
				}
			}
		} else {
			if debugw != nil {
				fmt.Fprintf(debugw, "- not preferring node with children: node not selected\n")
			}
		}
	}

	// try nodes of the parent id tasks
	if dbTask.DependsOn != nil {
		parentTasks := cloud.GetTasks(*dbTask.DependsOn, locks)
		for _, parentTask := range parentTasks {
			var parentTaskNodeTasks = parentTask.GetNodeTaskList(locks)
			for _, nodeTask := range parentTaskNodeTasks { //TODO prefer nodetask that is running
				var node = cloud.GetNode(nodeTask.DbNodeTask().Node, locks)
				if node == nil {
					continue
				}
				parentGroup = node.Group

				// check if the node is a candidate
				var candidate *Candidate
				for i, c := range candidates {
					if c.Node.Id == node.Id {
						candidate = &candidates[i]
						break
					}
				}
				if candidate != nil {
					if debugw != nil {
						fmt.Fprintf(debugw, "- using node with a parent\n")
					}
					return candidate.Node, candidate.SelectedResources, nil
				}
				if debugw != nil {
					fmt.Fprintf(debugw, "- not preferring node with a parent: node not selected\n")
				}
			}
		}
	}

	// try nodes in the same group as parent/children and then all nodes
	for _, filter := range []string{"parent", "children", "all"} {
		if parentGroup == "" && filter == "parent" {
			continue
		}
		if childrenGroup == "" && filter == "children" {
			continue
		}
		for i, c := range candidates {
			if filter == "parent" && c.Node.Group != parentGroup {
				continue
			}
			if filter == "children" && c.Node.Group != childrenGroup {
				continue
			}

			if filter == "children" {
				balanced, err := cloud.TestTaskOnNode(task, &candidates[i], candidates, selectedResource, testResourceBalance, locks)
				if err != nil || !balanced.(bool) {
					continue
				}
			}

			if debugw != nil {
				if filter == "parent" {
					fmt.Fprintf(debugw, "- selected node in the group where a parent is running: %s\n", c.Node.Id)
				} else if filter == "children" {
					fmt.Fprintf(debugw, "- selected node in the group where children are running: %s\n", c.Node.Id)
				}
			}
			return c.Node, c.SelectedResources, nil
		}
	}

	return nil, nil, ErrNoSatNodes
}

type Slice struct {
	sort.Interface
	idx []int
}

func (s Slice) Swap(i, j int) {
	s.Interface.Swap(i, j)
	s.idx[i], s.idx[j] = s.idx[j], s.idx[i]
}
func NewFloat64Slice(n ...float64) *Slice {
	ss := make([]float64, 0, len(n))
	ss = append(ss, n...)
	s := &Slice{
		Interface: sort.Float64Slice(ss),
		idx:       make([]int, len(n)),
	}
	for i := range s.idx {
		s.idx[i] = i
	}
	return s
}

func SelectResources(task *task.Task, node *node.Node, preferredResource string, locks controller.Locks) (common.SelectedResources, error) {
	selectedResources := common.NewSelectedResources()

	dbTask := task.DbTask()

	if dbTask.RequiredResources == nil {
		return selectedResources, nil
	}

	var indexes []bool
	var preferredIndexes *Slice
	var countOkIndexes int
	for resource, required := range *dbTask.RequiredResources {
		avail, _ := node.GetAvailableResource(resource, locks)

		if len(avail) == 0 {
			return nil, fmt.Errorf("node '%s' does not have resource '%s' for task '%s' (needed: %.2f)", node.Id, resource, task.DbTask(), required)
		}
		if len(avail) == 1 {
			if avail[0] < required {
				return nil, fmt.Errorf("node '%s' does not have enough resource '%s' (has: %.2f) for task '%s' (needed: %.2f)", node.Id, resource, avail[0], task.DbTask(), required)
			}
			selectedResources[resource] = 0
			continue
		}
		// XXX: temporary support for multiple resources (eg. GPU*)
		selectedResources[resource] = -1

		if resource == preferredResource {
			preferredIndexes = NewFloat64Slice(avail...)
			sort.Sort(sort.Reverse(preferredIndexes))
		}

		for index, avail_ := range avail {
			if len(indexes) == index {
				indexes = append(indexes, true)
				countOkIndexes++
			}
			if avail_ < required && indexes[index] {
				countOkIndexes--
				indexes[index] = false
			}

		}
	}

	if len(indexes) == 0 {
		return selectedResources, nil
	}
	if countOkIndexes < 0 {
		panic("negative countOkIndexes")
	}
	if countOkIndexes > 0 {
		if preferredIndexes != nil {
			for _, index := range preferredIndexes.idx {
				if indexes[index] {
					for resource, selectedIndex := range selectedResources {
						if selectedIndex == -1 {
							selectedResources[resource] = index
						}
					}
					return selectedResources, nil
				}
			}
		}

		r := rand.Int() % countOkIndexes // nolint:gosec
		var currOk int
		for index, ok := range indexes {
			if !ok {
				continue
			}
			if currOk != r {
				currOk++
				continue
			}
			for resource, selectedIndex := range selectedResources {
				if selectedIndex == -1 {
					selectedResources[resource] = index
				}
			}
			return selectedResources, nil
		}
	}
	return nil, fmt.Errorf("node %s does not have enough resources for task %s", node.Id, task.DbTask())
}

func (cloud *Cloud) EnqueueTaskCommand(task common.RunnableTask, node *node.Node) {
	select {
	case cloud.TaskDispatchQueue <- TaskCommand{
		Task:        task,
		Destination: node.Host,
	}:
	default:
		err := fmt.Errorf("dispatching queue full")
		task.Log(log.LOG_ERROR, "ERROR dispatching task: %s", err)
		cloud.FatalError(err)
	}
}

func (cloud *Cloud) HandleTasks() {
	select {
	case cloud.tasksBump <- true:
	default:
	}
}

func (cloud *Cloud) tasksHandler() {
	ticker := time.NewTicker(30 * time.Minute) //TODO remove ticker
	defer ticker.Stop()
	limitTicker := time.NewTicker(1 * time.Second) //TODO remove ticker
	defer limitTicker.Stop()

	for {
		select {
		case <-ticker.C:
		case <-cloud.tasksBump:
		}
		cloud.CheckTasks()

		<-limitTicker.C
	}
}

func (cloud *Cloud) CheckTasks() {
	var err error

	locks := controller.NewLocks()

	// copy taskList because it is modified during task handling (eg. creation of dummy nodeTasks)
	controller.AssertLock(locks.Task, controller.LockUnlocked)
	locks.RLockTasks()
	cloud.tasksMu.RLock()
	taskList := cloud.taskList.List()
	locks.RUnlockTasks()
	cloud.tasksMu.RUnlock()

	var unhandled bool
	for _, task := range taskList {
		_, err = cloud.HandleTask(task, nil, locks)
		controller.AssertLock(locks.Task, controller.LockUnlocked)
		if err != nil {
			unhandled = true
			if err != ErrNoSatNodes {
				task.Log(log.LOG_ERROR, "ERROR dispatching task '%s': %s", task.DbTask().Id, err) //TODO handle
			}
		}
	}
	controller.AssertLock(locks.Tasks, controller.LockUnlocked)
	if unhandled {
		cloud.HandleTasks()
	}
}

func (cloud *Cloud) HandleTask(task *task.Task, debugw io.Writer, locks controller.Locks) (bool, error) {
	dbTask := task.DbTask()

	if dbTask.Action == "run" {
		if !task.Runnable() {
			if debugw != nil {
				fmt.Fprintf(debugw, "exhausted limit for finite tasks\n")
			}
			return false, nil
		}
		controller.AssertLock(locks.Task, controller.LockUnlocked)
		locks.RLockTasks()
		cloud.tasksMu.RLock()

		running, _, zombie := task.GetRunning(controller.CountNew, locks)

		if zombie {
			if debugw != nil {
				fmt.Fprintf(debugw, "does not require any action (is a zombie)\n")
			}
			locks.RUnlockTasks()
			cloud.tasksMu.RUnlock()
			return false, nil
		}
		if cloud.isTaskIdRunning(dbTask.Id, 0, locks) {
			if debugw != nil {
				fmt.Fprintf(debugw, "does not require any action (taskId running)\n")
			}
			locks.RUnlockTasks()
			cloud.tasksMu.RUnlock()
			return false, nil
		}
		if running {
			if debugw != nil {
				fmt.Fprintf(debugw, "does not require any action (task starting)\n")
			}
			locks.RUnlockTasks()
			cloud.tasksMu.RUnlock()
			return false, nil
		}

		// do not plan higher-level tasks if at least one
		// lower-order task has running parents
		if dbTask.Level > 0 {
			controller.AssertLock(locks.Tasks, controller.LockRLocked)
			for _, altTask := range cloud.tasks[dbTask.Id] {
				altDbTask := altTask.DbTask()
				if altDbTask.Level >= dbTask.Level {
					continue
				}

				if altDbTask.DependsOn == nil && altDbTask.After == nil {
					continue
				}

				err := cloud.checkParents(altTask.DbTask(), locks)
				if err == nil {
					if debugw != nil {
						fmt.Fprintf(debugw, "does not require any action (lower level task available)\n") //TODO fix text
					}

					locks.RUnlockTasks()
					cloud.tasksMu.RUnlock()
					return false, nil
				}
			}
		}

		/*
			locks.RUnlockTasks()
			cloud.tasksMu.RUnlock()

			// check that parents are running
			AssertLock(locks.Task, LockUnlocked)
			locks.RLockTasks()
			cloud.tasksMu.RLock()
		*/
		err := cloud.checkParents(dbTask, locks)
		locks.RUnlockTasks()
		cloud.tasksMu.RUnlock()
		if err != nil {
			if debugw != nil {
				fmt.Fprintf(debugw, "does not require any action (%s)\n", err)
			}
			return false, nil
		}

		if debugw != nil {
			fmt.Fprintf(debugw, "run task '%s'\n", dbTask.Id)
			return true, nil
		}

		node, selectedResources, err := cloud.SelectNode(task, nil, locks)
		if err != nil {
			return false, err
		}

		var onlyCheckRunAttempt = false
		if debugw != nil {
			onlyCheckRunAttempt = true
		}
		if controller.RunLimitEnable && !task.AllowRunAttempt(onlyCheckRunAttempt, locks) {
			if debugw != nil {
				fmt.Fprintf(debugw, "non, too many attempts recently\n")
			}
			return false, nil
		}

		// set delay to let lower order Tasks to have a chance to start sooner
		var delay time.Duration
		controller.AssertLock(locks.Task, controller.LockUnlocked)
		locks.RLockTasks()
		cloud.tasksMu.RLock()
		for _, otherTask := range cloud.tasks[dbTask.Id] {
			if otherTask.DbTask().Order > dbTask.Order {
				continue
			}

			delay += otherTask.NodeTaskDelay(time.Now(), 5*time.Second, locks)
		}
		locks.RUnlockTasks()
		cloud.tasksMu.RUnlock()
		delay += time.Duration(gocommon.RandomInt(0, int(controller.TaskRandomStartDelayMax.Nanoseconds())/1e6)) * time.Millisecond

		task.UpdateDbTaskPrivateData(locks)

		runnableTask := common.RunnableTask{
			DbTask:            *task.DbTask(),
			SelectedResources: selectedResources,
			StartDelay:        delay,
		}
		cloud.EnqueueTaskCommand(runnableTask, node) //TODO if queue empty return err

		/* we need to take the required resources of the task into account right away
		 * and not wait until we receive the NodeTask from the task itself */
		cloud.AddDummyNodeTask(runnableTask, node, locks)

		return true, nil
	}

	if dbTask.Action != "wait" {
		nodeTasks := task.GetNodeTaskList(locks)
		var sent bool
		for _, nodeTask := range nodeTasks {
			dbNodeTask := nodeTask.DbNodeTask()

			if dbNodeTask.Revision > dbTask.Revision {
				continue
			}

			node := cloud.GetNode(dbNodeTask.Node, locks)
			targetTask := dbNodeTask.OriginTask
			targetTask.Action = dbTask.Action
			if debugw != nil {
				fmt.Fprintf(debugw, "command '%s' for task '%s' runnning on '%s'\n", dbTask.Action, dbNodeTask.OriginTask.Id, dbNodeTask.Node)
				return true, nil
			}
			cloud.EnqueueTaskCommand(targetTask, node)
			sent = true
		}
		return sent, nil
	}

	if debugw != nil {
		fmt.Fprintf(debugw, "does not require any action (action = %s)\n", dbTask.Action)
	}
	return false, nil
}

func (cloud *Cloud) checkParents(dbTask *common.DbTask, locks controller.Locks) error {
	if dbTask.DependsOn != nil || dbTask.After != nil {
		var parentId string
		var afterId string

		check := func(tasks map[int]*task.Task) bool {
			for _, task := range tasks {
				d := task.DbTask()
				//if d.Revision != dbTask.Revision {
				//	continue
				//}
				if d.Action != "wait" {
					return false
				}
			}
			return true
		}

		parentsDone := true
		if dbTask.DependsOn != nil {
			parentId = *dbTask.DependsOn
			tasks := cloud.getTasks(parentId, locks)
			if !check(tasks) {
				parentsDone = false
			}
		}
		if dbTask.After != nil {
			afterId = *dbTask.After
			tasks := cloud.getTasks(afterId, locks)
			if !check(tasks) {
				parentsDone = false
			}
		}

		if !parentsDone && dbTask.Finite && dbTask.RequiresFinishedDeps {
			return fmt.Errorf("parent not done")
		}

		// parent does not need to be running in case of finished finite tasks //TODO testcase
		if !(dbTask.Finite && parentsDone) { //TODO check if parents are finite, not this task
			if parentId != "" {
				if !cloud.isTaskIdRunning(parentId, controller.CountAnyRevision|controller.CountAnyLevel, locks) {
					return fmt.Errorf("parent in the 'dependsOn' field not running")
				}
			}
			if afterId != "" {
				if !cloud.isTaskIdRunning(afterId, controller.CountAnyRevision|controller.CountAnyLevel, locks) {
					return fmt.Errorf("parent in the 'after' field not running")
				}
			}
		}

	}
	return nil
}

func (cloud *Cloud) DoneTask(dbTask *common.DbTask, locks controller.Locks) {
	controller.AssertLock(locks.Task, controller.LockUnlocked)
	locks.RLockTasks()
	cloud.tasksMu.RLock()
	defer cloud.tasksMu.RUnlock()
	defer locks.RUnlockTasks()
	task := cloud.getTask(dbTask.Id, dbTask.DbId, locks)
	if task == nil {
		return
	}

	task.DbTask().Action = "wait" //TODO locking
}

func (cloud *Cloud) AddDummyNodeTask(task common.RunnableTask, node *node.Node, locks controller.Locks) *common.DbNodeTask {
	dummyDbNodeTask := &common.DbNodeTask{
		Task:         task.DbTask.DbId,
		Node:         node.Id,
		State:        "dummy",
		CreationTime: time.Now(),
		Id:           common.CreateNodeTaskId(&task.DbTask, node.Host),
		OriginTask:   task,
	}

	cloud.AddNodeTask(dummyDbNodeTask, locks)
	cloud.FixNodeLists(node, locks)

	return dummyDbNodeTask
}

func (cloud *Cloud) removeDummyNodeTask(dummyDbNodeTask *common.DbNodeTask, locks controller.Locks) {
	cloud.RemoveNodeTask(dummyDbNodeTask, locks)
}
