package cloud

import (
	"strings"
	"testing"
	"time"

	"github.com/matryer/is"

	"git.moderntv.eu/mcloud/system/common"
	"git.moderntv.eu/mcloud/system/controller"
)

// TestCompleteSchedulerResourceFlow tests the complete flow from scheduler to resource tracking
// including node selection, resource balancing, full node detection, and state transitions
func TestCompleteSchedulerResourceFlow(t *testing.T) {
	t.Parallel()
	is := is.New(t)

	t.Run("complete flow with multiple nodes and resource balancing", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		cloud := NewCloud(controller.NewMetrics())
		locks := controller.NewLocks()

		// Create multiple nodes with different resource configurations
		nodeA := createNode(t, cloud, common.MultiResources{
			"GPU": {1000.0, 2000.0},
			"CPU": {4000.0},
		}, &common.Flags{"test": true}, "nodeA")
		nodeA.Group = "groupA"

		nodeB := createNode(t, cloud, common.MultiResources{
			"GPU": {1500.0, 1000.0},
			"CPU": {6000.0},
		}, &common.Flags{"test": true}, "nodeB")
		nodeB.Group = "groupA"

		nodeC := createNode(t, cloud, common.MultiResources{
			"GPU": {500.0, 3000.0},
			"CPU": {2000.0},
		}, &common.Flags{"test": true}, "nodeC")
		nodeC.Group = "groupB"

		// Test 1: Initial resource availability
		gpuAvailA, _ := nodeA.GetAvailableResource("GPU", locks)
		is.Equal(gpuAvailA, []float64{1000.0, 2000.0})

		gpuAvailB, _ := nodeB.GetAvailableResource("GPU", locks)
		is.Equal(gpuAvailB, []float64{1500.0, 1000.0})

		gpuAvailC, _ := nodeC.GetAvailableResource("GPU", locks)
		is.Equal(gpuAvailC, []float64{500.0, 3000.0})

		// Test 2: Schedule first task - should select node with best resource ratio
		task1 := createTask(t, cloud, &common.DbTask{
			DbId:              1,
			Id:                "task1",
			Action:            "run",
			RequiredResources: &common.Resources{"GPU": 800.0, "CPU": 1000.0},
		})

		selectedNode, selectedResources, err := cloud.SelectNode(task1, nil, locks)
		is.NoErr(err)
		is.True(selectedNode != nil)

		// Should select nodeB (GPU index 0: 1500 > 800) or nodeA (GPU index 1: 2000 > 800)
		// Scheduler chooses based on resource scarcity ratio
		t.Logf("Task1 selected node: %s, resources: %v", selectedNode.Id, selectedResources)

		// Dispatch the task
		handled, err := cloud.HandleTask(task1, nil, locks)
		is.NoErr(err)
		is.True(handled)

		// Test 3: Verify resource reservation after task dispatch
		// The task should create a dummy node task for immediate resource reservation
		gpuAvailAfter, _ := selectedNode.GetAvailableResource("GPU", locks)
		t.Logf("GPU availability after task1 on %s: %v", selectedNode.Id, gpuAvailAfter)

		// Test 4: Schedule second task - test resource balancing
		task2 := createTask(t, cloud, &common.DbTask{
			DbId:              2,
			Id:                "task2",
			Action:            "run",
			RequiredResources: &common.Resources{"GPU": 600.0, "CPU": 1500.0},
		})

		selectedNode2, selectedResources2, err := cloud.SelectNode(task2, nil, locks)
		is.NoErr(err)
		is.True(selectedNode2 != nil)
		t.Logf("Task2 selected node: %s, resources: %v", selectedNode2.Id, selectedResources2)

		// Test 5: Try to schedule a task that won't fit anywhere
		taskTooBig := createTask(t, cloud, &common.DbTask{
			DbId:              3,
			Id:                "taskTooBig",
			Action:            "run",
			RequiredResources: &common.Resources{"GPU": 5000.0}, // Too big for any node
		})

		_, _, err = cloud.SelectNode(taskTooBig, nil, locks)
		is.True(err != nil) // Should fail with ErrNoSatNodes
		is.True(err == ErrNoSatNodes)

		// Test 6: Fill up a node completely and verify scheduler detects it's full
		taskFillNode := createTask(t, cloud, &common.DbTask{
			DbId:              4,
			Id:                "taskFillNode",
			Action:            "run",
			RequiredResources: &common.Resources{"GPU": 2500.0}, // Should fill remaining capacity
		})

		selectedNode3, _, err := cloud.SelectNode(taskFillNode, nil, locks)
		if err == nil {
			t.Logf("Task to fill node selected: %s", selectedNode3.Id)
			handled, err := cloud.HandleTask(taskFillNode, nil, locks)
			is.NoErr(err)
			is.True(handled)

			// Now try to schedule another task - should select a different node
			taskAfterFull := createTask(t, cloud, &common.DbTask{
				DbId:              5,
				Id:                "taskAfterFull",
				Action:            "run",
				RequiredResources: &common.Resources{"GPU": 400.0},
			})

			selectedNode4, _, err := cloud.SelectNode(taskAfterFull, nil, locks)
			is.NoErr(err)
			is.True(selectedNode4 != nil)
			is.True(selectedNode4.Id != selectedNode3.Id) // Should select different node
			t.Logf("Task after full node selected: %s", selectedNode4.Id)
		}
	})

	t.Run("resource balancing with debug output", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		cloud := NewCloud(controller.NewMetrics())
		locks := controller.NewLocks()

		// Create nodes with unbalanced resources
		_ = createNode(t, cloud, common.MultiResources{
			"GPU": {2000.0},
			"CPU": {8000.0},
		}, &common.Flags{"test": true}, "nodeA")

		_ = createNode(t, cloud, common.MultiResources{
			"GPU": {500.0},
			"CPU": {2000.0},
		}, &common.Flags{"test": true}, "nodeB")

		// Schedule a task with debug output
		task := createTask(t, cloud, &common.DbTask{
			DbId:              1,
			Id:                "debugTask",
			Action:            "run",
			RequiredResources: &common.Resources{"GPU": 400.0, "CPU": 1000.0},
		})

		var debugOutput strings.Builder
		selectedNode, selectedResources, err := cloud.SelectNode(task, &debugOutput, locks)
		is.NoErr(err)
		is.True(selectedNode != nil)

		debugStr := debugOutput.String()
		t.Logf("Debug output:\n%s", debugStr)

		// Verify debug output contains expected information
		is.True(strings.Contains(debugStr, "nodes ordered by"))
		is.True(strings.Contains(debugStr, "selecting candidate nodes"))
		is.True(strings.Contains(debugStr, "SELECTED"))

		t.Logf("Selected node: %s, resources: %v", selectedNode.Id, selectedResources)
	})

	t.Run("task state transitions and resource tracking", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		cloud := NewCloud(controller.NewMetrics())
		locks := controller.NewLocks()

		node := createNode(t, cloud, common.MultiResources{
			"GPU": {1000.0},
			"CPU": {4000.0},
		}, &common.Flags{"test": true}, "testNode")

		// Create and schedule a task
		task := createTask(t, cloud, &common.DbTask{
			DbId:              1,
			Id:                "stateTask",
			Action:            "run",
			RequiredResources: &common.Resources{"GPU": 500.0, "CPU": 2000.0},
		})

		// Schedule the task
		handled, err := cloud.HandleTask(task, nil, locks)
		is.NoErr(err)
		is.True(handled)

		// Verify dummy task was created and resources are reserved
		gpuAvail, _ := node.GetAvailableResource("GPU", locks)
		cpuAvail, _ := node.GetAvailableResource("CPU", locks)
		t.Logf("After scheduling - GPU: %v, CPU: %v", gpuAvail, cpuAvail)

		// Resources should be reserved (500 GPU, 2000 CPU)
		is.True(gpuAvail[0] <= 500.0)  // Should be 500 or less due to reservation
		is.True(cpuAvail[0] <= 2000.0) // Should be 2000 or less due to reservation

		// Simulate task starting (add real node task)
		now := time.Now()
		startTime := now.Add(-5 * time.Minute)

		dbNodeTask := &common.DbNodeTask{
			Id:             "realNodeTask",
			Task:           task.DbTask().DbId,
			Node:           node.Id,
			State:          "starting",
			StartTime:      &startTime,
			LastOnlineTime: &now,
			OriginTask: common.RunnableTask{
				DbTask:            *task.DbTask(),
				SelectedResources: common.SelectedResources{"GPU": 0, "CPU": 0},
			},
			ResourceUsage: common.MultiResources{
				"GPU": {100.0}, // Using 100 out of 500 required
				"CPU": {500.0}, // Using 500 out of 2000 required
			},
		}

		cloud.AddNodeTask(dbNodeTask, locks)

		// Verify resource calculation with real task
		gpuAvailAfterStart, _ := node.GetAvailableResource("GPU", locks)
		cpuAvailAfterStart, _ := node.GetAvailableResource("CPU", locks)
		t.Logf("After starting - GPU: %v, CPU: %v", gpuAvailAfterStart, cpuAvailAfterStart)

		// Should have immediate reservation: required - used = 500 - 100 = 400 GPU, 2000 - 500 = 1500 CPU
		is.True(gpuAvailAfterStart[0] <= 600.0)  // 1000 - 400 = 600
		is.True(cpuAvailAfterStart[0] <= 2500.0) // 4000 - 1500 = 2500

		// Simulate task transitioning to running (old enough to not be reserved)
		oldTime := now.Add(-30 * time.Second)
		dbNodeTask.State = "running"
		dbNodeTask.RunningSinceTime = &oldTime
		dbNodeTask.LastRunningTime = &now

		cloud.AddNodeTask(dbNodeTask, locks) // This updates the existing task

		// Verify resource calculation with non-reserved running task
		gpuAvailAfterRunning, _ := node.GetAvailableResource("GPU", locks)
		cpuAvailAfterRunning, _ := node.GetAvailableResource("CPU", locks)
		t.Logf("After running (non-reserved) - GPU: %v, CPU: %v", gpuAvailAfterRunning, cpuAvailAfterRunning)

		// Should have potential reserve instead of immediate reservation
		// Available = total - max_potential_reserve = 1000 - 400 = 600 GPU, 4000 - 1500 = 2500 CPU
		is.True(gpuAvailAfterRunning[0] <= 600.0)
		is.True(cpuAvailAfterRunning[0] <= 2500.0)

		// Remove the task and verify cleanup
		cloud.RemoveNodeTask(dbNodeTask, locks)

		gpuAvailAfterRemove, _ := node.GetAvailableResource("GPU", locks)
		cpuAvailAfterRemove, _ := node.GetAvailableResource("CPU", locks)
		t.Logf("After removal - GPU: %v, CPU: %v", gpuAvailAfterRemove, cpuAvailAfterRemove)

		// Should return to original capacity
		is.True(gpuAvailAfterRemove[0] >= 1000.0)
		is.True(cpuAvailAfterRemove[0] >= 4000.0)
	})

	t.Run("multi-dimensional resource selection and balancing", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		cloud := NewCloud(controller.NewMetrics())
		locks := controller.NewLocks()

		// Create node with multiple GPU indices
		node := createNode(t, cloud, common.MultiResources{
			"GPU": {1000.0, 2000.0, 500.0, 1500.0},
			"CPU": {8000.0},
		}, &common.Flags{"test": true}, "multiGPUNode")

		// Test preferred resource selection (should prefer highest available)
		task1 := createTask(t, cloud, &common.DbTask{
			DbId:              1,
			Id:                "task1",
			Action:            "run",
			RequiredResources: &common.Resources{"GPU": 400.0},
		})

		selectedNode, selectedResources, err := cloud.SelectNode(task1, nil, locks)
		is.NoErr(err)
		is.True(selectedNode != nil)
		is.Equal(selectedNode.Id, node.Id)

		// Should select GPU index 1 (2000.0) as it has the most available
		gpuIndex, hasGPU := selectedResources["GPU"]
		is.True(hasGPU)
		t.Logf("Selected GPU index: %d", gpuIndex)

		// Schedule the task
		handled, err := cloud.HandleTask(task1, nil, locks)
		is.NoErr(err)
		is.True(handled)

		// Verify resource reservation on correct index
		gpuAvail, _ := node.GetAvailableResource("GPU", locks)
		t.Logf("GPU availability after task1: %v", gpuAvail)

		// The selected index should have reduced availability
		is.True(gpuAvail[gpuIndex] < 2000.0)

		// Schedule another task that should select a different index
		task2 := createTask(t, cloud, &common.DbTask{
			DbId:              2,
			Id:                "task2",
			Action:            "run",
			RequiredResources: &common.Resources{"GPU": 800.0},
		})

		selectedNode2, selectedResources2, err := cloud.SelectNode(task2, nil, locks)
		is.NoErr(err)
		is.True(selectedNode2 != nil)

		gpuIndex2, hasGPU2 := selectedResources2["GPU"]
		is.True(hasGPU2)
		t.Logf("Task2 selected GPU index: %d", gpuIndex2)

		// Should select a different index or the same if it still has enough
		handled2, err := cloud.HandleTask(task2, nil, locks)
		is.NoErr(err)
		is.True(handled2)

		gpuAvailAfter2, _ := node.GetAvailableResource("GPU", locks)
		t.Logf("GPU availability after task2: %v", gpuAvailAfter2)
	})

	t.Run("node capability requirements", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		cloud := NewCloud(controller.NewMetrics())
		locks := controller.NewLocks()

		// Create nodes with different capabilities
		nodeWithCuda := createNode(t, cloud, common.MultiResources{
			"GPU": {1000.0},
		}, &common.Flags{"cuda": true, "test": true}, "cudaNode")

		_ = createNode(t, cloud, common.MultiResources{
			"GPU": {2000.0},
		}, &common.Flags{"cuda": false, "test": true}, "noCudaNode")

		// Task requiring CUDA
		cudaTask := createTask(t, cloud, &common.DbTask{
			DbId:              1,
			Id:                "cudaTask",
			Action:            "run",
			RequiredResources: &common.Resources{"GPU": 500.0},
			Requirements:      &common.Flags{"cuda": true},
		})

		selectedNode, _, err := cloud.SelectNode(cudaTask, nil, locks)
		is.NoErr(err)
		is.True(selectedNode != nil)
		is.Equal(selectedNode.Id, nodeWithCuda.Id) // Should select CUDA-capable node

		// Task not requiring CUDA (should select node with more resources)
		normalTask := createTask(t, cloud, &common.DbTask{
			DbId:              2,
			Id:                "normalTask",
			Action:            "run",
			RequiredResources: &common.Resources{"GPU": 500.0},
		})

		selectedNode2, _, err := cloud.SelectNode(normalTask, nil, locks)
		is.NoErr(err)
		is.True(selectedNode2 != nil)
		// Could select either node, but likely the one with more resources
		t.Logf("Normal task selected node: %s", selectedNode2.Id)
	})

	t.Run("disabled and non-running nodes", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		cloud := NewCloud(controller.NewMetrics())
		locks := controller.NewLocks()

		// Create enabled running node
		goodNode := createNode(t, cloud, common.MultiResources{
			"GPU": {1000.0},
		}, &common.Flags{"test": true}, "goodNode")

		// Create disabled node
		disabledNode := createNode(t, cloud, common.MultiResources{
			"GPU": {2000.0},
		}, &common.Flags{"test": true}, "disabledNode")

		// Disable the second node
		cloud.UpdateNode(&common.DbNode{
			Id:      disabledNode.Id,
			Enabled: false,
		}, locks)

		// Task should only be scheduled on enabled node
		task := createTask(t, cloud, &common.DbTask{
			DbId:              1,
			Id:                "testTask",
			Action:            "run",
			RequiredResources: &common.Resources{"GPU": 500.0},
		})

		selectedNode, _, err := cloud.SelectNode(task, nil, locks)
		is.NoErr(err)
		is.True(selectedNode != nil)
		is.Equal(selectedNode.Id, goodNode.Id) // Should only select enabled node
	})
}
