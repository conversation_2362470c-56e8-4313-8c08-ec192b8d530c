package controller

import (
	"testing"

	. "git.moderntv.eu/go/test"
	"git.moderntv.eu/mcloud/log"
)

func init() {
	log.SetLogger(&log.NullLogger{})
}

func TestSettingsSanity(t *testing.T) {
	t.Run("timers", func(t *testing.T) {
		//TODO add compensation for period time of messages
		ASSERT(t, NodeRemoveTimeout < NodeTaskRemoveTimeout, "dead node should be removed before any tasks are so that they are not replanned on the same node")
	})
}
