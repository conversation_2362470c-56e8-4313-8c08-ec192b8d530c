//go:build !production
// +build !production

package controller

import "fmt"

type LockState int8

const (
	LockUnlocked LockState = 0
	LockRLocked  LockState = 1
	LockLocked   LockState = 2
)

func lockLock(current *LockState) {
	if *current != LockUnlocked {
		panic("lock is already locked")
	}

	*current = LockLocked
}

func lockRLock(current *LockState) {
	if *current != LockUnlocked {
		panic("lock is already locked")
	}

	*current = LockRLocked
}

func lockUnlock(current *LockState) {
	if *current != LockLocked {
		panic("lock is not locked")
	}

	*current = LockUnlocked
}

func lockRUnlock(current *LockState) {
	if *current != LockRLocked {
		panic("lock is not locked for reading")
	}

	*current = LockUnlocked
}

func AssertLock(current LockState, required LockState) {
	if (required != LockRLocked || current != LockLocked) && current != required {
		panic(fmt.Sprintf("Want locking state %d, got %d", required, current))
	}
}

type Locks = *lockStruct

type lockStruct struct {
	Tasks      LockState
	Nodes      LockState
	Controller LockState
	Task       LockState
	Node       LockState
	NodeTask   LockState
}

// Tasks > Task
// Nodes > Node
// NodeTask

func NewLocks() Locks {
	return &lockStruct{}
}

///

func (l *lockStruct) AssertUnlocked() {
	AssertLock(l.Nodes, LockUnlocked)
	AssertLock(l.Node, LockUnlocked)
	AssertLock(l.Tasks, LockUnlocked)
	AssertLock(l.Task, LockUnlocked)
	AssertLock(l.NodeTask, LockUnlocked)
}

///

func (l *lockStruct) LockNodes() {
	AssertLock(l.Node, LockUnlocked)
	AssertLock(l.Tasks, LockUnlocked)
	AssertLock(l.Task, LockUnlocked)
	AssertLock(l.NodeTask, LockUnlocked)
	lockLock(&l.Nodes)
}

func (l *lockStruct) UnlockNodes() {
	lockUnlock(&l.Nodes)
}

func (l *lockStruct) RLockNodes() {
	AssertLock(l.Node, LockUnlocked)
	AssertLock(l.Tasks, LockUnlocked)
	AssertLock(l.Task, LockUnlocked)
	AssertLock(l.NodeTask, LockUnlocked)
	lockRLock(&l.Nodes)
}

func (l *lockStruct) RUnlockNodes() {
	lockRUnlock(&l.Nodes)
}

///

func (l *lockStruct) LockNode() {
	AssertLock(l.Tasks, LockUnlocked)
	AssertLock(l.Task, LockUnlocked)
	AssertLock(l.NodeTask, LockUnlocked)
	// AssertLock(l.Nodes, LockUnlocked)
	lockLock(&l.Node)
}

func (l *lockStruct) UnlockNode() {
	lockUnlock(&l.Node)
}

func (l *lockStruct) RLockNode() {
	AssertLock(l.Tasks, LockUnlocked)
	AssertLock(l.Task, LockUnlocked)
	AssertLock(l.NodeTask, LockUnlocked)
	// AssertLock(l.Nodes, LockUnlocked)
	lockRLock(&l.Node)
}

func (l *lockStruct) RUnlockNode() {
	lockRUnlock(&l.Node)
}

///

func (l *lockStruct) LockTask() {
	AssertLock(l.NodeTask, LockUnlocked)
	AssertLock(l.Nodes, LockUnlocked)
	AssertLock(l.Node, LockUnlocked)
	lockLock(&l.Task)
}

func (l *lockStruct) UnlockTask() {
	lockUnlock(&l.Task)
}

func (l *lockStruct) RLockTask() {
	AssertLock(l.NodeTask, LockUnlocked)
	AssertLock(l.Nodes, LockUnlocked)
	AssertLock(l.Node, LockUnlocked)
	lockRLock(&l.Task)
}

func (l *lockStruct) RUnlockTask() {
	lockRUnlock(&l.Task)
}

///

func (l *lockStruct) LockTasks() {
	AssertLock(l.Nodes, LockUnlocked)
	AssertLock(l.Node, LockUnlocked)
	AssertLock(l.Task, LockUnlocked)
	AssertLock(l.NodeTask, LockUnlocked)
	lockLock(&l.Tasks)
}

func (l *lockStruct) UnlockTasks() {
	lockUnlock(&l.Tasks)
}

func (l *lockStruct) RLockTasks() {
	AssertLock(l.Nodes, LockUnlocked)
	AssertLock(l.Node, LockUnlocked)
	AssertLock(l.Task, LockUnlocked)
	AssertLock(l.NodeTask, LockUnlocked)
	lockRLock(&l.Tasks)
}

func (l *lockStruct) RUnlockTasks() {
	lockRUnlock(&l.Tasks)
}

///

func (l *lockStruct) LockNodeTask() {
	AssertLock(l.Node, LockUnlocked)
	AssertLock(l.Nodes, LockUnlocked)
	AssertLock(l.Tasks, LockUnlocked)
	AssertLock(l.Task, LockUnlocked)
	lockLock(&l.NodeTask)
}

func (l *lockStruct) UnlockNodeTask() {
	lockUnlock(&l.NodeTask)
}

func (l *lockStruct) RLockNodeTask() {
	AssertLock(l.Node, LockUnlocked)
	AssertLock(l.Nodes, LockUnlocked)
	AssertLock(l.Tasks, LockUnlocked)
	AssertLock(l.Task, LockUnlocked)
	lockRLock(&l.NodeTask)
}

func (l *lockStruct) RUnlockNodeTask() {
	lockRUnlock(&l.NodeTask)
}

///

func (l *lockStruct) LockController() {
	AssertLock(l.NodeTask, LockUnlocked)
	AssertLock(l.Node, LockUnlocked)
	AssertLock(l.Nodes, LockUnlocked)
	AssertLock(l.Tasks, LockUnlocked)
	AssertLock(l.Task, LockUnlocked)
	lockLock(&l.Controller)
}

func (l *lockStruct) UnlockController() {
	AssertLock(l.NodeTask, LockUnlocked)
	AssertLock(l.Node, LockUnlocked)
	AssertLock(l.Nodes, LockUnlocked)
	AssertLock(l.Tasks, LockUnlocked)
	AssertLock(l.Task, LockUnlocked)
	lockUnlock(&l.Controller)
}

func (l *lockStruct) RLockController() {
	lockRLock(&l.Controller)
}

func (l *lockStruct) RUnlockController() {
	lockRUnlock(&l.Controller)
}
