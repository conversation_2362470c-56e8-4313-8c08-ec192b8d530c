package node

import (
	"testing"

	"github.com/matryer/is"
)

func TestPeakRunningRequirements(t *testing.T) {
	t.Parallel()
	is := is.New(t)

	t.Run("NewPeakRunningRequirements", func(t *testing.T) {
		t.<PERSON>llel()
		is := is.New(t)

		prr := NewPeakRunningRequirements()

		is.True(prr.maxReserve != nil)
		is.True(prr.taskReserves != nil)
	})

	t.Run("InitializeResource", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		prr := NewPeakRunningRequirements()

		// Initialize new resource
		prr.InitializeResource("GPU", 4)

		is.True(prr.maxReserve["GPU"] != nil)
		is.Equal(len(prr.maxReserve["GPU"]), 4)

		// All values should be zero
		for i := 0; i < 4; i++ {
			is.Equal(prr.maxReserve["GPU"][i], 0.0)
		}

		// Initialize existing resource with different size
		prr.InitializeResource("GPU", 6)
		is.Equal(len(prr.maxReserve["GPU"]), 6)
	})

	t.Run("AddTaskReserve", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		prr := NewPeakRunningRequirements()
		prr.InitializeResource("GPU", 4)

		// Add first task reserve
		reserves1 := []float64{10.0, 0.0, 20.0, 0.0}
		prr.AddTaskReserve(1, "GPU", reserves1)

		// Check max reserve (should match first task)
		maxReserve := prr.maxReserve["GPU"]
		is.Equal(maxReserve[0], 10.0)
		is.Equal(maxReserve[1], 0.0)
		is.Equal(maxReserve[2], 20.0)
		is.Equal(maxReserve[3], 0.0)

		// Check task reserves
		taskReserve := prr.taskReserves[1]["GPU"]
		is.True(taskReserve != nil)
		is.Equal(len(taskReserve), 4)
		is.Equal(taskReserve[0], 10.0)
		is.Equal(taskReserve[2], 20.0)

		// Add second task with higher reserves
		reserves2 := []float64{5.0, 25.0, 15.0, 30.0}
		prr.AddTaskReserve(2, "GPU", reserves2)

		// Check max reserve (should be maximum of both)
		is.Equal(maxReserve[0], 10.0) // max(10, 5)
		is.Equal(maxReserve[1], 25.0) // max(0, 25)
		is.Equal(maxReserve[2], 20.0) // max(20, 15)
		is.Equal(maxReserve[3], 30.0) // max(0, 30)
	})

	t.Run("AddTaskReserve edge cases", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		prr := NewPeakRunningRequirements()
		prr.InitializeResource("GPU", 2)

		// Empty reserves should be ignored
		prr.AddTaskReserve(1, "GPU", []float64{})
		is.Equal(prr.maxReserve["GPU"][0], 0.0)
		is.Equal(prr.maxReserve["GPU"][1], 0.0)

		// Nil reserves should be ignored
		prr.AddTaskReserve(2, "GPU", nil)
		is.Equal(prr.maxReserve["GPU"][0], 0.0)
		is.Equal(prr.maxReserve["GPU"][1], 0.0)

		// Multiple resources for same task
		prr.AddTaskReserve(3, "GPU", []float64{10.0, 20.0})
		prr.AddTaskReserve(3, "CPU", []float64{5.0, 15.0})

		is.True(prr.taskReserves[3]["GPU"] != nil)
		is.True(prr.taskReserves[3]["CPU"] != nil)
	})

	t.Run("RemoveTaskReserve", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		prr := NewPeakRunningRequirements()
		prr.InitializeResource("GPU", 3)

		// Add multiple tasks
		prr.AddTaskReserve(1, "GPU", []float64{10.0, 20.0, 30.0})
		prr.AddTaskReserve(2, "GPU", []float64{5.0, 25.0, 15.0})
		prr.AddTaskReserve(3, "GPU", []float64{15.0, 10.0, 20.0})

		// Max should be [15, 25, 30]
		maxReserve := prr.maxReserve["GPU"]
		is.Equal(maxReserve[0], 15.0)
		is.Equal(maxReserve[1], 25.0)
		is.Equal(maxReserve[2], 30.0)

		// Remove task 2 (had max at index 1 with 25.0)
		prr.RemoveTaskReserve(2, "GPU")

		// Task should be removed
		is.Equal(prr.taskReserves[2], map[string][]float64(nil))

		// Max should be recalculated - index 1 should now be 20.0 (max of remaining tasks)
		maxReserve = prr.GetMaxReserveForResource("GPU")
		is.Equal(maxReserve[0], 15.0) // max(10, 15) - unchanged
		is.Equal(maxReserve[1], 20.0) // max(20, 10) - recalculated from 25
		is.Equal(maxReserve[2], 30.0) // max(30, 20) - unchanged

		// Remove task 1 (has max at index 2 with 30.0)
		prr.RemoveTaskReserve(1, "GPU")

		// Max should be recalculated again
		maxReserve = prr.GetMaxReserveForResource("GPU")
		is.Equal(maxReserve[0], 15.0) // only task 3 remains
		is.Equal(maxReserve[1], 10.0) // only task 3 remains
		is.Equal(maxReserve[2], 20.0) // only task 3 remains
	})

	t.Run("RemoveTaskReserve edge cases", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		prr := NewPeakRunningRequirements()
		prr.InitializeResource("GPU", 2)

		// Remove non-existent task should not crash
		prr.RemoveTaskReserve(999, "GPU")
		// No task should be present
		is.Equal(prr.taskReserves[999], map[string][]float64(nil))

		// Remove non-existent resource from existing task should not crash
		prr.AddTaskReserve(1, "GPU", []float64{10.0, 20.0})
		prr.RemoveTaskReserve(1, "CPU")
		// Task 1 should still have GPU resource
		is.True(prr.taskReserves[1]["GPU"] != nil)
		is.Equal(prr.taskReserves[1]["CPU"], []float64(nil))
	})

	t.Run("RecalculateMaxReserveForResource", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		prr := NewPeakRunningRequirements()
		prr.InitializeResource("GPU", 3)

		// Add tasks
		prr.AddTaskReserve(1, "GPU", []float64{10.0, 20.0, 30.0})
		prr.AddTaskReserve(2, "GPU", []float64{5.0, 25.0, 15.0})
		prr.AddTaskReserve(3, "GPU", []float64{15.0, 10.0, 20.0})

		// Manually corrupt max reserve
		prr.maxReserve["GPU"][0] = 999.0
		prr.maxReserve["GPU"][1] = 888.0
		prr.maxReserve["GPU"][2] = 777.0

		// Recalculate
		prr.RecalculateMaxReserveForResource("GPU")

		// Should be correct again
		maxReserve := prr.maxReserve["GPU"]
		is.Equal(maxReserve[0], 15.0) // max(10, 5, 15)
		is.Equal(maxReserve[1], 25.0) // max(20, 25, 10)
		is.Equal(maxReserve[2], 30.0) // max(30, 15, 20)

		// Recalculate non-existent resource
		prr.RecalculateMaxReserveForResource("NONEXISTENT")
		// Should not crash
	})

	t.Run("GetMaxReserve", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		prr := NewPeakRunningRequirements()
		prr.InitializeResource("GPU", 2)
		prr.InitializeResource("CPU", 3)

		// Add some reserves
		prr.AddTaskReserve(1, "GPU", []float64{10.0, 20.0})
		prr.AddTaskReserve(1, "CPU", []float64{5.0, 15.0, 25.0})

		result := prr.GetMaxReserve()

		// Values should match
		is.Equal(len(result["GPU"]), 2)
		is.Equal(result["GPU"][0], 10.0)
		is.Equal(result["GPU"][1], 20.0)

		is.Equal(len(result["CPU"]), 3)
		is.Equal(result["CPU"][0], 5.0)
		is.Equal(result["CPU"][1], 15.0)
		is.Equal(result["CPU"][2], 25.0)

		// Modifying result should not affect original
		result["GPU"][0] = 999.0
		is.Equal(prr.maxReserve["GPU"][0], 10.0)
	})

	t.Run("GetMaxReserveForResource", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		prr := NewPeakRunningRequirements()
		prr.InitializeResource("GPU", 3)

		prr.AddTaskReserve(1, "GPU", []float64{10.0, 20.0, 30.0})
		prr.AddTaskReserve(2, "GPU", []float64{5.0, 25.0, 15.0})

		result := prr.GetMaxReserveForResource("GPU")

		is.Equal(len(result), 3)
		is.Equal(result[0], 10.0) // max(10, 5)
		is.Equal(result[1], 25.0) // max(20, 25)
		is.Equal(result[2], 30.0) // max(30, 15)

		// Non-existent resource
		nilResult := prr.GetMaxReserveForResource("NONEXISTENT")
		is.Equal(nilResult, []float64(nil))
	})

	t.Run("Complex scenario", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		prr := NewPeakRunningRequirements()
		prr.InitializeResource("GPU", 2)

		// Add multiple tasks
		prr.AddTaskReserve(1, "GPU", []float64{10.0, 20.0})
		prr.AddTaskReserve(2, "GPU", []float64{15.0, 5.0})
		prr.AddTaskReserve(3, "GPU", []float64{8.0, 25.0})

		// Max should be [15, 25]
		maxReserve := prr.GetMaxReserveForResource("GPU")
		is.Equal(maxReserve[0], 15.0)
		is.Equal(maxReserve[1], 25.0)

		// Remove task 2 with current max at index 0 (15.0)
		prr.RemoveTaskReserve(2, "GPU")

		// Max should now be automatically recalculated to [10, 25]
		maxReserve = prr.GetMaxReserveForResource("GPU")
		is.Equal(maxReserve[0], 10.0) // max(10, 8) - remaining tasks 1 and 3
		is.Equal(maxReserve[1], 25.0) // max(20, 25) - remaining tasks 1 and 3

		// Remove task 3 with current max at index 1 (25.0)
		prr.RemoveTaskReserve(3, "GPU")

		// Max should now be automatically recalculated to [10, 20]
		maxReserve = prr.GetMaxReserveForResource("GPU")
		is.Equal(maxReserve[0], 10.0) // only task 1 remains
		is.Equal(maxReserve[1], 20.0) // only task 1 remains
	})

	t.Run("GetReservationInfo", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		prr := NewPeakRunningRequirements()
		prr.InitializeResource("GPU", 2)

		// Add task reserves
		prr.AddTaskReserve(1, "GPU", []float64{10.0, 20.0})
		prr.AddTaskReserve(2, "GPU", []float64{5.0, 25.0})

		// Get reservation info
		info := prr.GetReservationInfo("GPU")
		is.True(info != nil)

		// Should have (backup) entry with max reserves
		backup := info["(backup)"]
		is.True(backup != nil)
		is.Equal(len(backup), 2)
		is.Equal(backup[0], 10.0) // max from tasks 1 and 2
		is.Equal(backup[1], 25.0) // max from tasks 1 and 2

		// Test with non-existent resource
		info = prr.GetReservationInfo("CPU")
		is.True(info != nil)
		is.Equal(len(info), 0) // Should be empty
	})
}
