package node

// PeakRunningRequirements holds the maximum potential reserves for running tasks.
// These reserves are calculated as the maximum difference between required resources
// and actually used resources across all running tasks (those with NeedsReservedResource false).
type PeakRunningRequirements struct {
	maxReserve   map[string][]float64
	taskReserves map[int]map[string][]float64 // taskID -> resourceType -> reserves
}

// NewPeakRunningRequirements creates a new peak running requirements tracker
func NewPeakRunningRequirements() PeakRunningRequirements {
	return PeakRunningRequirements{
		maxReserve:   make(map[string][]float64),
		taskReserves: make(map[int]map[string][]float64),
	}
}

// InitializeResource initializes max reserve tracking for a resource type
func (prr *PeakRunningRequirements) InitializeResource(resourceType string, size int) {
	if prr.maxReserve[resourceType] == nil {
		prr.maxReserve[resourceType] = initializeSlice(size)
	} else {
		slice := prr.maxReserve[resourceType]
		ensureSliceSize(&slice, size)
		prr.maxReserve[resourceType] = slice
	}
}

// AddTaskReserve adds potential reserves for a task and updates max reserves incrementally
func (prr *PeakRunningRequirements) AddTaskReserve(taskID int, resourceType string, reserves []float64) {
	if len(reserves) == 0 {
		return
	}

	// Initialize task reserves if needed
	if prr.taskReserves[taskID] == nil {
		prr.taskReserves[taskID] = make(map[string][]float64)
	}

	// Store task reserves
	prr.taskReserves[taskID][resourceType] = copySlice(reserves)

	// Update max reserves incrementally
	maxSlice := prr.maxReserve[resourceType]
	for i, reserve := range reserves {
		if i < len(maxSlice) && reserve > maxSlice[i] {
			maxSlice[i] = reserve
		}
	}
}

// RemoveTaskReserve removes potential reserves for a task
func (prr *PeakRunningRequirements) RemoveTaskReserve(taskID int, resourceType string) {
	if prr.taskReserves[taskID] == nil {
		return
	}

	reserves := prr.taskReserves[taskID][resourceType]
	if reserves == nil {
		return
	}

	// Check if this task had the maximum reserve - if so, we need to recalculate
	needsRecalc := false
	maxSlice := prr.maxReserve[resourceType]
	for i, reserve := range reserves {
		if i < len(maxSlice) && reserve > 0 && reserve >= maxSlice[i] {
			needsRecalc = true
			break
		}
	}

	// Remove task reserves
	delete(prr.taskReserves[taskID], resourceType)
	if len(prr.taskReserves[taskID]) == 0 {
		delete(prr.taskReserves, taskID)
	}

	// Recalculate if needed
	if needsRecalc {
		prr.RecalculateMaxReserveForResource(resourceType)
	}
}

// RecalculateMaxReserveForResource recalculates max reserve for a specific resource
func (prr *PeakRunningRequirements) RecalculateMaxReserveForResource(resourceType string) {
	maxSlice := prr.maxReserve[resourceType]
	if maxSlice == nil {
		return
	}

	// Reset max reserves for this resource
	for i := range maxSlice {
		maxSlice[i] = 0
	}

	// Recalculate from all tasks for this resource only
	for _, taskResources := range prr.taskReserves {
		reserves := taskResources[resourceType]
		for i, val := range reserves {
			if i < len(maxSlice) && val > maxSlice[i] {
				maxSlice[i] = val
			}
		}
	}
}

// GetMaxReserve returns a copy of max reserves
func (prr *PeakRunningRequirements) GetMaxReserve() map[string][]float64 {
	result := make(map[string][]float64)
	for res, values := range prr.maxReserve {
		result[res] = copySlice(values)
	}
	return result
}

// GetMaxReserveForResource returns max reserve for a specific resource
func (prr *PeakRunningRequirements) GetMaxReserveForResource(resourceType string) []float64 {
	return prr.maxReserve[resourceType]
}

// GetReservationInfo returns debug information about maximum reserves for a specific resource
func (prr *PeakRunningRequirements) GetReservationInfo(resourceType string) map[string][]float64 {
	info := make(map[string][]float64)
	if maxReserve := prr.maxReserve[resourceType]; maxReserve != nil {
		info["(backup)"] = copySlice(maxReserve)
	}
	return info
}
