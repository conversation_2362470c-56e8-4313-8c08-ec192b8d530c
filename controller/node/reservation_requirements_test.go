package node

import (
	"testing"

	"github.com/matryer/is"
)

func TestReservationRequirements(t *testing.T) {
	t.<PERSON>llel()
	is := is.New(t)

	t.Run("NewReservationRequirements", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		rr := NewReservationRequirements()

		is.True(rr.totalReserved != nil)
		is.True(rr.reservationInfo != nil)
	})

	t.Run("InitializeResource", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		rr := NewReservationRequirements()

		// Initialize new resource
		rr.InitializeResource("GPU", 4)

		is.True(rr.totalReserved["GPU"] != nil)
		is.Equal(len(rr.totalReserved["GPU"]), 4)
		is.True(rr.reservationInfo["GPU"] != nil)

		// All values should be zero
		for i := 0; i < 4; i++ {
			is.Equal(rr.totalReserved["GPU"][i], 0.0)
		}

		// Initialize existing resource with different size
		rr.InitializeResource("GPU", 6)
		is.Equal(len(rr.totalReserved["GPU"]), 6)

		// Original values should be preserved, new ones zero
		for i := 0; i < 6; i++ {
			is.Equal(rr.totalReserved["GPU"][i], 0.0)
		}
	})

	t.Run("AddReservation", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		rr := NewReservationRequirements()
		rr.InitializeResource("GPU", 4)

		// Add first reservation
		reservations1 := []float64{10.0, 0.0, 20.0, 0.0}
		rr.AddReservation("GPU", "task1", reservations1)

		// Check total reserved
		total := rr.totalReserved["GPU"]
		is.Equal(total[0], 10.0)
		is.Equal(total[1], 0.0)
		is.Equal(total[2], 20.0)
		is.Equal(total[3], 0.0)

		// Check reservation info
		info := rr.reservationInfo["GPU"]["task1"]
		is.True(info != nil)
		is.Equal(len(info), 4)
		is.Equal(info[0], 10.0)
		is.Equal(info[2], 20.0)

		// Add second reservation
		reservations2 := []float64{5.0, 15.0, 0.0, 25.0}
		rr.AddReservation("GPU", "task2", reservations2)

		// Check total reserved (should be sum)
		is.Equal(total[0], 15.0) // 10 + 5
		is.Equal(total[1], 15.0) // 0 + 15
		is.Equal(total[2], 20.0) // 20 + 0
		is.Equal(total[3], 25.0) // 0 + 25
	})

	t.Run("AddReservation edge cases", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		rr := NewReservationRequirements()
		rr.InitializeResource("GPU", 2)

		// Empty reservations should be ignored
		rr.AddReservation("GPU", "task1", []float64{})
		is.Equal(rr.totalReserved["GPU"][0], 0.0)
		is.Equal(rr.totalReserved["GPU"][1], 0.0)

		// Nil reservations should be ignored
		rr.AddReservation("GPU", "task2", nil)
		is.Equal(rr.totalReserved["GPU"][0], 0.0)
		is.Equal(rr.totalReserved["GPU"][1], 0.0)

		// Negative values should not be added (only positive values are added)
		rr.AddReservation("GPU", "task3", []float64{-5.0, 10.0})
		is.Equal(rr.totalReserved["GPU"][0], 0.0)  // Negative value ignored
		is.Equal(rr.totalReserved["GPU"][1], 10.0) // Positive value added
	})

	t.Run("RemoveReservation", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		rr := NewReservationRequirements()
		rr.InitializeResource("GPU", 3)

		// Add reservations
		rr.AddReservation("GPU", "task1", []float64{10.0, 20.0, 30.0})
		rr.AddReservation("GPU", "task2", []float64{5.0, 0.0, 15.0})

		// Verify totals
		total := rr.totalReserved["GPU"]
		is.Equal(total[0], 15.0)
		is.Equal(total[1], 20.0)
		is.Equal(total[2], 45.0)

		// Remove first reservation
		rr.RemoveReservation("GPU", "task1", []float64{10.0, 20.0, 30.0})

		// Check totals
		is.Equal(total[0], 5.0)  // 15 - 10
		is.Equal(total[1], 0.0)  // 20 - 20
		is.Equal(total[2], 15.0) // 45 - 30

		// Check reservation info
		is.Equal(rr.reservationInfo["GPU"]["task1"], []float64(nil))
		is.True(rr.reservationInfo["GPU"]["task2"] != nil)
	})

	t.Run("GetTotalReserved", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		rr := NewReservationRequirements()
		rr.InitializeResource("GPU", 2)
		rr.InitializeResource("CPU", 3)

		// Add some reservations
		rr.AddReservation("GPU", "task1", []float64{10.0, 20.0})
		rr.AddReservation("CPU", "task1", []float64{5.0, 15.0, 25.0})

		result := rr.GetTotalReserved()

		// Values should match
		is.Equal(len(result["GPU"]), 2)
		is.Equal(result["GPU"][0], 10.0)
		is.Equal(result["GPU"][1], 20.0)

		is.Equal(len(result["CPU"]), 3)
		is.Equal(result["CPU"][0], 5.0)
		is.Equal(result["CPU"][1], 15.0)
		is.Equal(result["CPU"][2], 25.0)

		// Modifying result should not affect original
		result["GPU"][0] = 999.0
		is.Equal(rr.totalReserved["GPU"][0], 10.0)
	})

	t.Run("GetReservationInfo", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		rr := NewReservationRequirements()
		rr.InitializeResource("GPU", 2)

		rr.AddReservation("GPU", "task1", []float64{10.0, 20.0})
		rr.AddReservation("GPU", "task2", []float64{5.0, 15.0})

		info := rr.GetReservationInfo("GPU")
		is.True(info != nil)
		is.Equal(len(info), 2)

		task1Info := info["task1"]
		is.Equal(len(task1Info), 2)
		is.Equal(task1Info[0], 10.0)
		is.Equal(task1Info[1], 20.0)

		// Non-existent resource
		nilInfo := rr.GetReservationInfo("NONEXISTENT")
		is.Equal(nilInfo, map[string][]float64(nil))
	})

	t.Run("SetNodeReservation", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		rr := NewReservationRequirements()
		rr.InitializeResource("GPU", 2)

		// Set node reservation
		rr.SetNodeReservation("GPU", []float64{5.0, 10.0})

		// Check reservation info for node
		info := rr.GetReservationInfo("GPU")
		nodeInfo := info["(node)"]
		is.True(nodeInfo != nil)
		is.Equal(len(nodeInfo), 2)
		is.Equal(nodeInfo[0], 5.0)
		is.Equal(nodeInfo[1], 10.0)

		// Empty reservations should be ignored
		rr.SetNodeReservation("GPU", []float64{})
		is.Equal(len(info), 1) // Should still have (node) key

		// Nil reservations should be ignored
		rr.SetNodeReservation("GPU", nil)
		is.Equal(len(info), 1) // Should still have (node) key
	})
}
