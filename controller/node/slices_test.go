package node

import (
	"fmt"
	"testing"

	"github.com/matryer/is"
)

func TestSliceFunctions(t *testing.T) {
	t.Parallel()
	is := is.New(t)

	t.Run("InitializeSlice", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		// Test zero size
		slice := initializeSlice(0)
		is.Equal(len(slice), 0)
		is.Equal(cap(slice), 0)

		// Test positive size
		slice = initializeSlice(5)
		is.Equal(len(slice), 5)
		is.Equal(cap(slice), 5)
		for i := 0; i < 5; i++ {
			is.Equal(slice[i], 0.0)
		}

		// Test large size
		slice = initializeSlice(1000)
		is.Equal(len(slice), 1000)
		for i := 0; i < 1000; i++ {
			is.Equal(slice[i], 0.0)
		}
	})

	t.Run("CopySlice", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		// Test nil slice
		copied := copySlice(nil)
		is.Equal(copied, []float64(nil))

		// Test empty slice
		empty := []float64{}
		copied = copySlice(empty)
		is.Equal(len(copied), 0)
		is.True(copied != nil) // Should not be nil, but empty

		// Test slice with values
		original := []float64{1.0, 2.5, 3.7, 4.2}
		copied = copySlice(original)
		is.Equal(len(copied), len(original))
		for i := range original {
			is.Equal(copied[i], original[i])
		}

		// Verify it's a deep copy (modifying copy doesn't affect original)
		copied[0] = 999.0
		is.Equal(original[0], 1.0) // Original should be unchanged
	})

	t.Run("EnsureSliceSize", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		t.Run("expand slice", func(t *testing.T) {
			slice := []float64{1.0, 2.0}
			ensureSliceSize(&slice, 5)

			is.Equal(len(slice), 5)
			is.Equal(slice[0], 1.0)
			is.Equal(slice[1], 2.0)
			is.Equal(slice[2], 0.0) // New elements should be zero
			is.Equal(slice[3], 0.0)
			is.Equal(slice[4], 0.0)
		})

		t.Run("shrink slice", func(t *testing.T) {
			slice := []float64{1.0, 2.0, 3.0, 4.0, 5.0}
			ensureSliceSize(&slice, 3)

			is.Equal(len(slice), 3)
			is.Equal(slice[0], 1.0)
			is.Equal(slice[1], 2.0)
			is.Equal(slice[2], 3.0)
		})

		t.Run("same size", func(t *testing.T) {
			original := []float64{1.0, 2.0, 3.0}
			slice := make([]float64, len(original))
			copy(slice, original)

			ensureSliceSize(&slice, 3)

			is.Equal(len(slice), 3)
			is.Equal(slice[0], 1.0)
			is.Equal(slice[1], 2.0)
			is.Equal(slice[2], 3.0)
		})

		t.Run("zero size", func(t *testing.T) {
			slice := []float64{1.0, 2.0, 3.0}
			ensureSliceSize(&slice, 0)

			is.Equal(len(slice), 0)
		})

		t.Run("expand with sufficient capacity", func(t *testing.T) {
			// Create slice with extra capacity
			slice := make([]float64, 2, 10)
			slice[0] = 1.0
			slice[1] = 2.0

			ensureSliceSize(&slice, 5)

			is.Equal(len(slice), 5)
			is.Equal(slice[0], 1.0)
			is.Equal(slice[1], 2.0)
			is.Equal(slice[2], 0.0)
			is.Equal(slice[3], 0.0)
			is.Equal(slice[4], 0.0)
		})

		t.Run("expand beyond capacity", func(t *testing.T) {
			slice := []float64{1.0, 2.0}
			originalCap := cap(slice)

			ensureSliceSize(&slice, originalCap+5)

			is.Equal(len(slice), originalCap+5)
			is.Equal(slice[0], 1.0)
			is.Equal(slice[1], 2.0)
			// Rest should be zero
			for i := 2; i < len(slice); i++ {
				is.Equal(slice[i], 0.0)
			}
		})
	})
}

func BenchmarkSliceFunctions(b *testing.B) {
	b.Run("InitializeSlice", func(b *testing.B) {
		sizes := []int{10, 100, 1000, 10000}
		for _, size := range sizes {
			b.Run(fmt.Sprintf("size_%d", size), func(b *testing.B) {
				b.ResetTimer()
				for i := 0; i < b.N; i++ {
					_ = initializeSlice(size)
				}
			})
		}
	})

	b.Run("CopySlice", func(b *testing.B) {
		sizes := []int{10, 100, 1000, 10000}
		for _, size := range sizes {
			slice := make([]float64, size)
			for i := range slice {
				slice[i] = float64(i)
			}

			b.Run(fmt.Sprintf("size_%d", size), func(b *testing.B) {
				b.ResetTimer()
				for i := 0; i < b.N; i++ {
					_ = copySlice(slice)
				}
			})
		}
	})

	b.Run("EnsureSliceSize", func(b *testing.B) {
		b.Run("expand", func(b *testing.B) {
			for i := 0; i < b.N; i++ {
				slice := []float64{1.0, 2.0}
				ensureSliceSize(&slice, 1000)
			}
		})

		b.Run("shrink", func(b *testing.B) {
			for i := 0; i < b.N; i++ {
				slice := make([]float64, 1000)
				ensureSliceSize(&slice, 10)
			}
		})
	})
}
