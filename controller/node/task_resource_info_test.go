package node

import (
	"testing"

	"github.com/matryer/is"
)

func TestTaskResourceInfo(t *testing.T) {
	t.Parallel()
	is := is.New(t)

	t.Run("struct creation", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		// Test empty struct
		info := &TaskResourceInfo{}
		is.Equal(info.Reserved, []float64(nil))
		is.Equal(info.Reserve, []float64(nil))

		// Test with initialized slices
		reserved := []float64{10.0, 20.0, 30.0}
		reserve := []float64{5.0, 15.0, 25.0}
		
		info = &TaskResourceInfo{
			Reserved: reserved,
			Reserve:  reserve,
		}
		
		is.Equal(len(info.Reserved), 3)
		is.Equal(len(info.Reserve), 3)
		is.Equal(info.Reserved[0], 10.0)
		is.Equal(info.Reserved[1], 20.0)
		is.Equal(info.Reserved[2], 30.0)
		is.Equal(info.Reserve[0], 5.0)
		is.Equal(info.Reserve[1], 15.0)
		is.Equal(info.Reserve[2], 25.0)
	})

	t.Run("independent slices", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		info := &TaskResourceInfo{
			Reserved: []float64{1.0, 2.0},
			Reserve:  []float64{3.0, 4.0},
		}

		// Modify Reserved, should not affect Reserve
		info.Reserved[0] = 999.0
		is.Equal(info.Reserve[0], 3.0) // Should be unchanged
		
		// Modify Reserve, should not affect Reserved
		info.Reserve[1] = 888.0
		is.Equal(info.Reserved[1], 2.0) // Should be unchanged
	})
}

func TestHasReservations(t *testing.T) {
	t.Parallel()
	is := is.New(t)

	t.Run("nil slice", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		result := hasReservations(nil)
		is.Equal(result, false)
	})

	t.Run("empty slice", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		result := hasReservations([]float64{})
		is.Equal(result, false)
	})

	t.Run("all zeros", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		result := hasReservations([]float64{0.0, 0.0, 0.0})
		is.Equal(result, false)
	})

	t.Run("has positive values", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		result := hasReservations([]float64{0.0, 5.0, 0.0})
		is.Equal(result, true)

		result = hasReservations([]float64{1.0, 0.0, 0.0})
		is.Equal(result, true)

		result = hasReservations([]float64{0.0, 0.0, 10.5})
		is.Equal(result, true)
	})

	t.Run("has negative values", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		// Negative values should not count as reservations
		result := hasReservations([]float64{0.0, -5.0, 0.0})
		is.Equal(result, false)

		result = hasReservations([]float64{-1.0, 0.0, 0.0})
		is.Equal(result, false)
	})

	t.Run("mixed values", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		// Should return true if any positive value exists
		result := hasReservations([]float64{-1.0, 0.0, 5.0})
		is.Equal(result, true)

		result = hasReservations([]float64{0.0, -10.0, 0.0, 2.5})
		is.Equal(result, true)
	})

	t.Run("very small positive values", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		// Even very small positive values should count
		result := hasReservations([]float64{0.0, 0.0001, 0.0})
		is.Equal(result, true)

		result = hasReservations([]float64{1e-10, 0.0})
		is.Equal(result, true)
	})

	t.Run("large slice", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		// Test with large slice - all zeros
		largeSlice := make([]float64, 10000)
		result := hasReservations(largeSlice)
		is.Equal(result, false)

		// Test with large slice - one positive value at the end
		largeSlice[9999] = 1.0
		result = hasReservations(largeSlice)
		is.Equal(result, true)
	})
}

func BenchmarkHasReservations(b *testing.B) {
	scenarios := []struct {
		name  string
		slice []float64
	}{
		{
			name:  "small_all_zeros",
			slice: []float64{0.0, 0.0, 0.0, 0.0, 0.0},
		},
		{
			name:  "small_has_positive",
			slice: []float64{0.0, 0.0, 5.0, 0.0, 0.0},
		},
		{
			name:  "medium_all_zeros",
			slice: make([]float64, 100),
		},
		{
			name:  "large_all_zeros",
			slice: make([]float64, 10000),
		},
	}

	// Set up medium slice with positive value
	mediumWithPositive := make([]float64, 100)
	mediumWithPositive[50] = 10.0
	scenarios = append(scenarios, struct {
		name  string
		slice []float64
	}{
		name:  "medium_has_positive",
		slice: mediumWithPositive,
	})

	// Set up large slice with positive value at end (worst case)
	largeWithPositive := make([]float64, 10000)
	largeWithPositive[9999] = 10.0
	scenarios = append(scenarios, struct {
		name  string
		slice []float64
	}{
		name:  "large_has_positive_at_end",
		slice: largeWithPositive,
	})

	for _, scenario := range scenarios {
		b.Run(scenario.name, func(b *testing.B) {
			b.ResetTimer()
			for i := 0; i < b.N; i++ {
				_ = hasReservations(scenario.slice)
			}
		})
	}
}
