package node

// ReservationRequirements holds the sum of all reservations for non-running tasks.
// These reservations are calculated as the sum of differences between required resources
// and actually used resources across all non-running tasks (those with NeedsReservedResource true).
type ReservationRequirements struct {
	totalReserved   map[string][]float64
	reservationInfo map[string]map[string][]float64
}

// NewReservationRequirements creates a new reservation requirements tracker
func NewReservationRequirements() ReservationRequirements {
	return ReservationRequirements{
		totalReserved:   make(map[string][]float64),
		reservationInfo: make(map[string]map[string][]float64),
	}
}

// InitializeResource initializes reservation tracking for a resource type
func (rr *ReservationRequirements) InitializeResource(resourceType string, size int) {
	if rr.totalReserved[resourceType] == nil {
		rr.totalReserved[resourceType] = initializeSlice(size)
	} else {
		slice := rr.totalReserved[resourceType]
		ensureSliceSize(&slice, size)
		rr.totalReserved[resourceType] = slice
	}

	if rr.reservationInfo[resourceType] == nil {
		rr.reservationInfo[resourceType] = make(map[string][]float64)
	}
}

// AddReservation adds a reservation for a specific task
func (rr *ReservationRequirements) AddReservation(resourceType, taskID string, reservations []float64) {
	if len(reservations) == 0 {
		return
	}

	// Update total reserved
	totalSlice := rr.totalReserved[resourceType]
	for i, reservation := range reservations {
		if i < len(totalSlice) && reservation > 0 {
			totalSlice[i] += reservation
		}
	}

	// Update reservation info
	taskSlice := rr.reservationInfo[resourceType][taskID]
	ensureSliceSize(&taskSlice, len(totalSlice))
	rr.reservationInfo[resourceType][taskID] = taskSlice
	copy(taskSlice, reservations)
}

// RemoveReservation removes a reservation for a specific task
func (rr *ReservationRequirements) RemoveReservation(resourceType, taskID string, reservations []float64) {
	if len(reservations) == 0 {
		return
	}

	// Update total reserved
	totalSlice := rr.totalReserved[resourceType]
	for i, reservation := range reservations {
		if i < len(totalSlice) && reservation > 0 {
			totalSlice[i] -= reservation
		}
	}

	// Remove from reservation info
	if rr.reservationInfo[resourceType] != nil {
		delete(rr.reservationInfo[resourceType], taskID)
	}
}

// GetTotalReserved returns a copy of total reserved resources
func (rr *ReservationRequirements) GetTotalReserved() map[string][]float64 {
	result := make(map[string][]float64)
	for res, values := range rr.totalReserved {
		result[res] = copySlice(values)
	}
	return result
}

// GetReservationInfo returns reservation info for a specific resource
func (rr *ReservationRequirements) GetReservationInfo(resourceType string) map[string][]float64 {
	return rr.reservationInfo[resourceType]
}

// SetNodeReservation sets node-level reservation (difference between free and available)
func (rr *ReservationRequirements) SetNodeReservation(resourceType string, reservations []float64) {
	if len(reservations) == 0 {
		return
	}

	nodeSlice := rr.reservationInfo[resourceType]["(node)"]
	ensureSliceSize(&nodeSlice, len(reservations))
	rr.reservationInfo[resourceType]["(node)"] = nodeSlice
	copy(nodeSlice, reservations)
}
