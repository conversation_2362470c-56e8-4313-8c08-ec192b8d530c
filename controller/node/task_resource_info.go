package node

// TaskResourceInfo holds information about resource usage for a specific task
type TaskResourceInfo struct {
	Reserved []float64 // Amount reserved for this task (immediate allocation)
	Reserve  []float64 // Amount that could be reserved (potential maximum usage)
}

// hasReservations checks if a slice has any non-zero values
func hasReservations(slice []float64) bool {
	for _, val := range slice {
		if val > 0 {
			return true
		}
	}
	return false
}
