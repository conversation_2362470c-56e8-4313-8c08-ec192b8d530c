package node

// ensureSliceSize ensures slice has the correct size, initializing new elements to zero
func ensureSliceSize(slice *[]float64, size int) {
	if len(*slice) != size {
		if cap(*slice) >= size {
			oldLen := len(*slice)
			*slice = (*slice)[:size]
			// Clear newly visible elements
			for i := oldLen; i < size; i++ {
				(*slice)[i] = 0
			}
		} else {
			newSlice := make([]float64, size)
			copy(newSlice, *slice)
			*slice = newSlice
		}
	}
}

// initializeSlice creates a new slice with the specified size, all elements set to zero
func initializeSlice(size int) []float64 {
	return make([]float64, size)
}

// copySlice creates a deep copy of the input slice
func copySlice(src []float64) []float64 {
	if src == nil {
		return nil
	}
	dst := make([]float64, len(src))
	copy(dst, src)
	return dst
}
