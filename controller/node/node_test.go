package node

import (
	"testing"
	"time"

	"github.com/matryer/is"

	"git.moderntv.eu/mcloud/system/common"
)

func TestNewNode(t *testing.T) {
	t.Parallel()
	is := is.New(t)

	dbNode := common.DbNode{
		Id:      "test-node-1",
		Host:    "test-host",
		State:   "running",
		Enabled: true,
		FreeResources: map[string][]float64{
			"GPU": {1000.0, 2000.0},
			"CPU": {100.0, 200.0},
		},
	}

	node := NewNode(dbNode)

	is.True(node != nil)
	is.Equal(node.Id, "test-node-1")
	is.Equal(node.Host, "test-host")
	is.Equal(node.State, "running")
	is.Equal(node.Enabled, true)
	is.True(node.resources != nil)
	is.True(node.nodeTasks != nil)
	is.Equal(len(node.nodeTasks), 0)
}

func TestNodeBasicOperations(t *testing.T) {
	t.<PERSON>llel()
	is := is.New(t)

	// Note: These tests are simplified because the real Node methods require
	// controller.Locks which is a complex dependency. In a real test environment,
	// you would need to mock or provide proper lock implementations.

	dbNode := common.DbNode{
		Id:      "test-node-1",
		Host:    "test-host",
		State:   "running",
		Enabled: true,
		FreeResources: map[string][]float64{
			"GPU": {1000.0},
		},
	}

	node := NewNode(dbNode)

	t.Run("initial state", func(t *testing.T) {
		is := is.New(t)

		// Test that node is properly initialized
		is.Equal(node.Id, "test-node-1")
		is.Equal(node.Host, "test-host")
		is.Equal(node.State, "running")
		is.Equal(node.Enabled, true)
		is.True(node.resources != nil)
		is.Equal(len(node.nodeTasks), 0)
	})

	t.Run("resource initialization", func(t *testing.T) {
		is := is.New(t)

		// Test that resources are properly initialized
		is.True(node.resources != nil)

		// Update resources (this doesn't require locks in the underlying implementation)
		freeResources := map[string][]float64{
			"GPU": {1000.0, 2000.0},
			"CPU": {500.0},
		}

		// Direct call to resources (bypassing the Node wrapper that requires locks)
		node.resources.UpdateResources(freeResources, nil)

		// Verify resources were updated
		available, _ := node.resources.GetAvailable("GPU")
		is.Equal(len(available), 2)
		is.Equal(available[0], 1000.0)
		is.Equal(available[1], 2000.0)

		cpuAvailable, _ := node.resources.GetAvailable("CPU")
		is.Equal(len(cpuAvailable), 1)
		is.Equal(cpuAvailable[0], 500.0)
	})
}

func TestNodeResourceOperations(t *testing.T) {
	t.Parallel()
	is := is.New(t)

	dbNode := common.DbNode{
		Id:      "test-node-1",
		Host:    "test-host",
		State:   "running",
		Enabled: true,
	}

	node := NewNode(dbNode)

	// Test direct resource operations (without locks)
	t.Run("resource updates", func(t *testing.T) {
		is := is.New(t)

		freeResources := map[string][]float64{
			"GPU":    {1000.0, 2000.0, 3000.0},
			"CPU":    {100.0, 200.0},
			"Memory": {8000.0},
		}

		availableResources := map[string][]float64{
			"GPU":    {900.0, 1800.0, 2700.0}, // Some resources already used
			"CPU":    {80.0, 180.0},
			"Memory": {7000.0},
		}

		node.resources.UpdateResources(freeResources, availableResources)

		// Test GPU resources
		gpuAvail, gpuInfo := node.resources.GetAvailable("GPU")
		is.Equal(len(gpuAvail), 3)
		is.Equal(gpuAvail[0], 900.0)
		is.Equal(gpuAvail[1], 1800.0)
		is.Equal(gpuAvail[2], 2700.0)

		// Should have node reservation info
		is.True(gpuInfo != nil)
		nodeReservation := gpuInfo["(node)"]
		is.True(nodeReservation != nil)
		is.Equal(nodeReservation[0], 100.0) // 1000 - 900
		is.Equal(nodeReservation[1], 200.0) // 2000 - 1800
		is.Equal(nodeReservation[2], 300.0) // 3000 - 2700

		// Test CPU resources
		cpuAvail, _ := node.resources.GetAvailable("CPU")
		is.Equal(len(cpuAvail), 2)
		is.Equal(cpuAvail[0], 80.0)
		is.Equal(cpuAvail[1], 180.0)

		// Test Memory resources
		memAvail, _ := node.resources.GetAvailable("Memory")
		is.Equal(len(memAvail), 1)
		is.Equal(memAvail[0], 7000.0)
	})

	t.Run("task resource management", func(t *testing.T) {
		is := is.New(t)

		// Setup resources
		node.resources.UpdateResources(map[string][]float64{
			"GPU": {1000.0, 2000.0},
		}, nil)

		// Create a test task
		now := time.Now()
		oldTime := now.Add(-30 * time.Second)

		requiredResources := common.Resources{
			"GPU": 500.0,
		}

		selectedResources := common.SelectedResources{
			"GPU": 0, // Use first GPU
		}

		dbTask := common.DbTask{
			DbId:              1,
			Id:                "task-1",
			RequiredResources: &requiredResources,
		}

		dbNodeTask := &common.DbNodeTask{
			Id:               "node-task-1",
			Task:             1,
			Node:             "test-node-1",
			State:            "running",
			LastOnlineTime:   &now,
			RunningSinceTime: &oldTime, // Old enough to not be reserved
			OriginTask: common.RunnableTask{
				DbTask:            dbTask,
				SelectedResources: selectedResources,
			},
			ResourceUsage: common.MultiResources{
				"GPU": {100.0, 0.0}, // Using 100 out of 500 required
			},
		}

		// Add task
		node.resources.AddTask(dbNodeTask)

		// Check available resources (should be reduced by max reserve)
		available, _ := node.resources.GetAvailable("GPU")
		is.Equal(available[0], 600.0)  // 1000 - (500-100) = 600
		is.Equal(available[1], 2000.0) // Second GPU unchanged

		// Remove task
		node.resources.RemoveTask(dbNodeTask)

		// Check available resources (should be restored)
		available, _ = node.resources.GetAvailable("GPU")
		is.Equal(available[0], 1000.0) // Restored
		is.Equal(available[1], 2000.0) // Unchanged
	})
}

func BenchmarkNewNode(b *testing.B) {
	dbNode := common.DbNode{
		Id:      "test-node-1",
		Host:    "test-host",
		State:   "running",
		Enabled: true,
		FreeResources: map[string][]float64{
			"GPU": {1000.0, 2000.0},
			"CPU": {100.0, 200.0},
		},
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = NewNode(dbNode)
	}
}
