package node

import (
	"testing"
	"time"

	"github.com/matryer/is"

	"git.moderntv.eu/mcloud/system/common"
)

func TestNeedsReservedResources(t *testing.T) {
	t.<PERSON>llel()
	is := is.New(t)

	now := time.Now()
	oldTime := now.Add(-30 * time.Second)
	recentTime := now.Add(-10 * time.Second)

	t.Run("nil LastOnlineTime", func(t *testing.T) {
		t.<PERSON>()
		is := is.New(t)

		dbNodeTask := &common.DbNodeTask{
			LastOnlineTime: nil,
			State:          "running",
		}

		result := NeedsReservedResources(dbNodeTask)
		is.Equal(result, true) // Should be reserved when LastOnlineTime is nil
	})

	t.Run("dummy state", func(t *testing.T) {
		t.<PERSON>()
		is := is.New(t)

		dbNodeTask := &common.DbNodeTask{
			LastOnlineTime: &now,
			State:          "dummy",
		}

		result := NeedsReservedResources(dbNodeTask)
		is.Equal(result, true) // Should be reserved for dummy state
	})

	t.Run("initializing state", func(t *testing.T) {
		t.<PERSON>()
		is := is.New(t)

		dbNodeTask := &common.DbNodeTask{
			LastOnlineTime: &now,
			State:          "initializing",
		}

		result := NeedsReservedResources(dbNodeTask)
		is.Equal(result, true) // Should be reserved for initializing state
	})

	t.Run("starting state", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		dbNodeTask := &common.DbNodeTask{
			LastOnlineTime: &now,
			State:          "starting",
		}

		result := NeedsReservedResources(dbNodeTask)
		is.Equal(result, true) // Should be reserved for starting state
	})

	t.Run("running state - nil RunningSinceTime", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		dbNodeTask := &common.DbNodeTask{
			LastOnlineTime:   &now,
			State:            "running",
			RunningSinceTime: nil,
		}

		result := NeedsReservedResources(dbNodeTask)
		is.Equal(result, true) // Should be reserved when RunningSinceTime is nil
	})

	t.Run("running state - recent RunningSinceTime", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		dbNodeTask := &common.DbNodeTask{
			LastOnlineTime:   &now,
			State:            "running",
			RunningSinceTime: &recentTime, // 10 seconds ago (< 15 seconds)
		}

		result := NeedsReservedResources(dbNodeTask)
		is.Equal(result, true) // Should be reserved when running for less than 15 seconds
	})

	t.Run("running state - old RunningSinceTime", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		dbNodeTask := &common.DbNodeTask{
			LastOnlineTime:   &now,
			State:            "running",
			RunningSinceTime: &oldTime, // 30 seconds ago (> 15 seconds)
		}

		result := NeedsReservedResources(dbNodeTask)
		is.Equal(result, false) // Should not be reserved when running for more than 15 seconds
	})

	t.Run("completed state", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		dbNodeTask := &common.DbNodeTask{
			LastOnlineTime:   &now,
			State:            "completed",
			RunningSinceTime: &oldTime,
		}

		result := NeedsReservedResources(dbNodeTask)
		is.Equal(result, false) // Should not be reserved for completed state
	})

	t.Run("failed state", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		dbNodeTask := &common.DbNodeTask{
			LastOnlineTime:   &now,
			State:            "failed",
			RunningSinceTime: &oldTime,
		}

		result := NeedsReservedResources(dbNodeTask)
		is.Equal(result, false) // Should not be reserved for failed state
	})

	t.Run("edge case - exactly 15 seconds", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		exactTime := now.Add(-15 * time.Second)
		dbNodeTask := &common.DbNodeTask{
			LastOnlineTime:   &now,
			State:            "running",
			RunningSinceTime: &exactTime,
		}

		result := NeedsReservedResources(dbNodeTask)
		is.Equal(result, false) // Should not be reserved when exactly 15 seconds (>= 15 seconds)
	})

	t.Run("edge case - just under 15 seconds", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		justUnder := now.Add(-14*time.Second - 999*time.Millisecond)
		dbNodeTask := &common.DbNodeTask{
			LastOnlineTime:   &now,
			State:            "running",
			RunningSinceTime: &justUnder,
		}

		result := NeedsReservedResources(dbNodeTask)
		is.Equal(result, true) // Should be reserved when just under 15 seconds
	})

	t.Run("unknown state", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		dbNodeTask := &common.DbNodeTask{
			LastOnlineTime:   &now,
			State:            "unknown",
			RunningSinceTime: &oldTime,
		}

		result := NeedsReservedResources(dbNodeTask)
		is.Equal(result, false) // Should not be reserved for unknown state
	})

	t.Run("empty state", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		dbNodeTask := &common.DbNodeTask{
			LastOnlineTime:   &now,
			State:            "",
			RunningSinceTime: &oldTime,
		}

		result := NeedsReservedResources(dbNodeTask)
		is.Equal(result, false) // Should not be reserved for empty state
	})
}

func BenchmarkNeedsReservedResources(b *testing.B) {
	now := time.Now()
	oldTime := now.Add(-30 * time.Second)
	recentTime := now.Add(-10 * time.Second)

	scenarios := []struct {
		name string
		task *common.DbNodeTask
	}{
		{
			name: "nil_last_online_time",
			task: &common.DbNodeTask{
				LastOnlineTime: nil,
				State:          "running",
			},
		},
		{
			name: "starting_state",
			task: &common.DbNodeTask{
				LastOnlineTime: &now,
				State:          "starting",
			},
		},
		{
			name: "running_recent",
			task: &common.DbNodeTask{
				LastOnlineTime:   &now,
				State:            "running",
				RunningSinceTime: &recentTime,
			},
		},
		{
			name: "running_old",
			task: &common.DbNodeTask{
				LastOnlineTime:   &now,
				State:            "running",
				RunningSinceTime: &oldTime,
			},
		},
		{
			name: "completed",
			task: &common.DbNodeTask{
				LastOnlineTime:   &now,
				State:            "completed",
				RunningSinceTime: &oldTime,
			},
		},
	}

	for _, scenario := range scenarios {
		b.Run(scenario.name, func(b *testing.B) {
			b.ResetTimer()
			for i := 0; i < b.N; i++ {
				_ = NeedsReservedResources(scenario.task)
			}
		})
	}
}
