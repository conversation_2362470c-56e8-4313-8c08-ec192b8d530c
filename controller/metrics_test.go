package controller

import (
	"testing"

	"github.com/prometheus/client_golang/prometheus"
)

func TestPendingMetric(t *testing.T) {
	metric := &pendingMetric{
		msgsDesc: prometheus.NewDesc(
			MetricsNamespace+"_nats_pending_messages",
			"Current number of messages pending for subscription",
			[]string{"type"},
			nil,
		),
		bytesDesc: prometheus.NewDesc(
			MetricsNamespace+"_nats_pending_bytes",
			"Current number of message bytes pending for subscription",
			[]string{"type"},
			nil,
		),
	}

	metric.AddSubscription("test", func() (msgs int, bytes int, err error) {
		return 2, 100, nil
	})

	ch := make(chan prometheus.Metric, 10)
	metric.Collect(ch)
	close(ch)

	result := make([]prometheus.Metric, 0, 10)
	for m := range ch {
		result = append(result, m)
	}

	if len(result) != 2 {
		t.<PERSON><PERSON>rf("want %d metrics, %d metrics", 2, len(result))
	}
}
