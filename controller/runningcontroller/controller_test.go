package runningcontroller

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"reflect"
	"testing"
	"time"

	"git.moderntv.eu/mcloud/system/common"
	"git.moderntv.eu/mcloud/system/controller"
	"git.moderntv.eu/mcloud/system/controller/cloud"
	"git.moderntv.eu/mcloud/system/controller/db"
	"git.moderntv.eu/mcloud/system/pkg/natsmock"
	"git.moderntv.eu/mcloud/system/pkg/testdb"
	"github.com/nats-io/nats.go"
	"github.com/prometheus/client_golang/prometheus"
	io_prometheus_client "github.com/prometheus/client_model/go"
)

type ControllerTest struct {
	Controller *RunningController
	Cloud      *cloud.Cloud
	DB         *db.DB
	Nats       *nats.EncodedConn
}

func (c ControllerTest) Close() {
	locks := controller.NewLocks()
	c.DB.Disconnect()
	c.Controller.Close(locks)
}

func GetController(t *testing.T) ControllerTest {
	cloud := cloud.NewCloud(controller.NewMetrics())
	connURL := testdb.InitializeDB("controller", "testdata/db-structure.sql")

	db := db.NewDB(connURL, testdb.DbLogger{})
	db.Connect()

	nc := natsmock.Connect()
	db.WaitForConnection()

	metrics := controller.NewMetrics()

	notifyServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		fmt.Printf("Task notification received\n")
	}))

	controller, err := New("testing", "test", cloud, db, nc, metrics, notifyServer.URL, 1, 0, 5)
	if err != nil {
		t.Fatal(err)
	}

	enc, err := nats.NewEncodedConn(nc, nats.JSON_ENCODER)
	if err != nil {
		t.Fatal(err)
	}

	return ControllerTest{
		Controller: controller,
		Cloud:      cloud,
		DB:         db,
		Nats:       enc,
	}
}

func TestFiniteTaskState(t *testing.T) {
	ct := GetController(t)
	defer ct.Close()

	locks := controller.NewLocks()

	ct.Controller.SetState(StateMaster, locks)

	now := time.Now()

	ct.Cloud.AddNode(&common.DbNode{
		Id:         "node_test1",
		Host:       "test1",
		State:      "running",
		LastOnline: timePtr(now),
		Enabled:    true,
	}, locks)

	ct.Cloud.AddTask(&common.DbTask{
		Id:           "task:123",
		DbId:         123,
		Class:        stringPtr("ffmpeg"),
		Action:       "plan",
		Resource:     "test_xy",
		ResourceCode: "test",
		Finite:       true,
	}, false, locks)

	selectedResources := common.SelectedResources{
		"CPU": 0,
		"mem": 0,
	}

	resourceUsage := common.MultiResources{
		"CPU": []float64{12},
		"mem": []float64{10},
	}

	// {Task:349956 Node:node_shi-dev2 Revision:1680231239 State:running Warnings: LastOnlineTime:2023-06-23 08:11:07.177262781 +0200 CEST LastRunningTime:2023-06-23 08:11:07.177262781 +0200 CEST
	// StartTime:2023-03-31 04:53:59.295280672 +0200 CEST SelectedResources:map[CPU:0 mem:0]
	// ResourceUsage:map[CPU:[0.3859708142857143] disk:[6.29425048828125e-05] mem:[50.81640625]] PrivateData:<nil> Status:
	// OriginTask:{DbTask:{DbId:349956 Id:nova_cinema_stream_67:1-11-hls:store Class:0xc000b50b40 Resource:nova_cinema_stream_67 ResourceCode:nova_cinema
	// Alternative:0xc000b50b50 Order:0 Level:0 Revision:1680231239 Action:run Finite:false Creation:<nil> Requirements:0xc00012aea0 RequiredResources:0xc00012aea8 Parameters:0xc000b50bb0
	// Priority:0xc000b5a878 Comment:store of 1-11-hls DependsOn:0xc000b50bc0 After:<nil> RequiresFinishedDeps:false PrivateData:<nil>} SelectedResources:map[CPU:0 mem:0] StartDelay:108ms}
	// ApiData: Id:task_shi-dev2_nova_cinema_stream_67:1-11-hls:store__1680231239 CreationTime:2023-03-31 04:53:59.175864704 +0200 CEST RunningSinceTime:2023-03-31 04:54:04.181725073 +0200 CEST}
	nodeTask := common.DbNodeTask{
		Task:              123,
		Node:              "node_test1",
		Revision:          10,
		State:             "done",
		StartTime:         timePtr(now.Add(-60 * time.Second)),
		LastOnlineTime:    timePtr(now),
		LastRunningTime:   timePtr(now),
		SelectedResources: selectedResources,
		ResourceUsage:     resourceUsage,

		OriginTask: common.RunnableTask{
			DbTask: common.DbTask{
				DbId:         123,
				Id:           "task:123",
				Class:        stringPtr("ffmpeg"),
				Resource:     "test_xy",
				ResourceCode: "teset",
				Alternative:  stringPtr(""),
				Finite:       true,
			},
			SelectedResources: selectedResources,
		},
		Id:               "task:123",
		CreationTime:     now.Add(-60 * time.Second),
		RunningSinceTime: timePtr(now.Add(-60 * time.Second)),
	}
	err := ct.Nats.Publish("task_state.test1", nodeTask)
	if err != nil {
		t.Fatal(err)
	}
	time.Sleep(500 * time.Millisecond)

	err = ct.Controller.DbPush(locks)
	if err != nil {
		t.Fatal(err)
	}

	time.Sleep(200 * time.Millisecond)

	storedNodeTask := getDBNodeTask(ct.Controller.Db, 123)
	if storedNodeTask == nil {
		t.Fatalf("Node task not found")
	}

	if storedNodeTask.Node != nodeTask.Node {
		t.Errorf("want node %s, got %s", nodeTask.Node, storedNodeTask.Node)
	}

	if !reflect.DeepEqual(storedNodeTask.ResourceUsage, resourceUsage) {
		t.Errorf("want resource usage %+v, got %+v", resourceUsage, storedNodeTask.ResourceUsage)
	}

	if !reflect.DeepEqual(storedNodeTask.SelectedResources, selectedResources) {
		t.Errorf("want selected resources %+v, got %+v", selectedResources, storedNodeTask.SelectedResources)
	}

	messageCount := GetHistogramCount(ct.Controller.metrics.ReceivedNatsMessages.WithLabelValues("task_state"))
	if messageCount != 1 {
		t.Errorf("want NATS message count %d, got %d", 1, messageCount)
	}
	locks.AssertUnlocked()
}

func TestTaskState(t *testing.T) {
	ct := GetController(t)
	defer ct.Close()

	locks := controller.NewLocks()
	ct.Controller.SetState(StateMaster, locks)

	now := time.Now()

	ct.Cloud.AddNode(&common.DbNode{
		Id:         "node_test1",
		Host:       "test1",
		State:      "running",
		LastOnline: timePtr(now),
		Enabled:    true,
	}, locks)

	ct.Cloud.AddTask(&common.DbTask{
		Id:           "task:123",
		DbId:         123,
		Class:        stringPtr("ffmpeg"),
		Action:       "plan",
		Resource:     "test_xy",
		ResourceCode: "test",
	}, false, locks)

	selectedResources := common.SelectedResources{
		"CPU": 0,
		"mem": 0,
	}

	resourceUsage := common.MultiResources{
		"CPU": []float64{12},
		"mem": []float64{10},
	}

	// {Task:349956 Node:node_shi-dev2 Revision:1680231239 State:running Warnings: LastOnlineTime:2023-06-23 08:11:07.177262781 +0200 CEST LastRunningTime:2023-06-23 08:11:07.177262781 +0200 CEST
	// StartTime:2023-03-31 04:53:59.295280672 +0200 CEST SelectedResources:map[CPU:0 mem:0]
	// ResourceUsage:map[CPU:[0.3859708142857143] disk:[6.29425048828125e-05] mem:[50.81640625]] PrivateData:<nil> Status:
	// OriginTask:{DbTask:{DbId:349956 Id:nova_cinema_stream_67:1-11-hls:store Class:0xc000b50b40 Resource:nova_cinema_stream_67 ResourceCode:nova_cinema
	// Alternative:0xc000b50b50 Order:0 Level:0 Revision:1680231239 Action:run Finite:false Creation:<nil> Requirements:0xc00012aea0 RequiredResources:0xc00012aea8 Parameters:0xc000b50bb0
	// Priority:0xc000b5a878 Comment:store of 1-11-hls DependsOn:0xc000b50bc0 After:<nil> RequiresFinishedDeps:false PrivateData:<nil>} SelectedResources:map[CPU:0 mem:0] StartDelay:108ms}
	// ApiData: Id:task_shi-dev2_nova_cinema_stream_67:1-11-hls:store__1680231239 CreationTime:2023-03-31 04:53:59.175864704 +0200 CEST RunningSinceTime:2023-03-31 04:54:04.181725073 +0200 CEST}
	nodeTask := common.DbNodeTask{
		Task:              123,
		Node:              "node_test1",
		Revision:          10,
		State:             "running",
		StartTime:         timePtr(now.Add(-60 * time.Second)),
		LastOnlineTime:    timePtr(now),
		LastRunningTime:   timePtr(now),
		SelectedResources: selectedResources,
		ResourceUsage:     resourceUsage,
		PrivateData:       stringPtr("zkouska"),
		OriginTask: common.RunnableTask{
			DbTask: common.DbTask{
				DbId:         123,
				Id:           "task:123",
				Class:        stringPtr("ffmpeg"),
				Resource:     "test_xy",
				ResourceCode: "teset",
				Alternative:  stringPtr(""),
				PrivateData:  stringPtr("zkouska"),
			},
			SelectedResources: selectedResources,
		},
		Id:               "task:123",
		CreationTime:     now.Add(-60 * time.Second),
		RunningSinceTime: timePtr(now.Add(-60 * time.Second)),
	}
	err := ct.Nats.Publish("task_state.test1", nodeTask)
	if err != nil {
		t.Fatal(err)
	}
	time.Sleep(500 * time.Millisecond)

	err = ct.Controller.DbPush(locks)
	if err != nil {
		t.Fatal(err)
	}

	time.Sleep(200 * time.Millisecond)

	storedNodeTask := getDBNodeTask(ct.Controller.Db, 123)
	if storedNodeTask == nil {
		t.Fatalf("Node task not found")
	}

	if storedNodeTask.Node != nodeTask.Node {
		t.Errorf("want node %s, got %s", nodeTask.Node, storedNodeTask.Node)
	}

	if !reflect.DeepEqual(storedNodeTask.ResourceUsage, resourceUsage) {
		t.Errorf("want resource usage %+v, got %+v", resourceUsage, storedNodeTask.ResourceUsage)
	}

	if !reflect.DeepEqual(storedNodeTask.SelectedResources, selectedResources) {
		t.Errorf("want selected resources %+v, got %+v", selectedResources, storedNodeTask.SelectedResources)
	}

	messageCount := GetHistogramCount(ct.Controller.metrics.ReceivedNatsMessages.WithLabelValues("task_state"))
	if messageCount != 1 {
		t.Errorf("want NATS message count %d, got %d", 1, messageCount)
	}
	locks.AssertUnlocked()
}

func timePtr(t time.Time) *time.Time {
	return &t
}

func stringPtr(s string) *string {
	return &s
}

func getDBNodeTask(db *db.DB, taskID int) *common.DbNodeTask {
	rows, err := db.DB().Query("SELECT task, originNode, revision, state, selectedResources, resourceUsage FROM NodeTasks WHERE task = ?", taskID)
	if err != nil {
		panic(err)
	}

	for rows.Next() {
		var nodeTask common.DbNodeTask
		var (
			selectedResources []byte
			resourceUsage     []byte
		)

		err = rows.Scan(&nodeTask.Task, &nodeTask.Node, &nodeTask.Revision, &nodeTask.State, &selectedResources, &resourceUsage)
		if err != nil {
			panic(err)
		}

		json.Unmarshal(selectedResources, &nodeTask.SelectedResources)
		json.Unmarshal(resourceUsage, &nodeTask.ResourceUsage)

		if nodeTask.Task == taskID {
			return &nodeTask
		}
	}

	return nil
}

func GetHistogramCount(metric prometheus.Observer) uint64 {
	m := io_prometheus_client.Metric{}
	metric.(prometheus.Metric).Write(&m)
	if m.GetHistogram().SampleCount == nil {
		return 0
	}
	return *m.GetHistogram().SampleCount
}
