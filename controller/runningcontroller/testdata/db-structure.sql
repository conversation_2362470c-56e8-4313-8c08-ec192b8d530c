-- moderntvcloud.Accounts definition

CREATE TABLE IF NOT EXISTS `Accounts` (
  `id` varchar(15) NOT NULL,
  `name` varchar(100) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- moderntvcloud.Controllers definition

CREATE TABLE IF NOT EXISTS `Controllers` (
  `id` varchar(255) NOT NULL,
  `type` varchar(20) NOT NULL DEFAULT 'taskController',
  `enabled` tinyint(4) NOT NULL DEFAULT 1,
  `host` varchar(100) NOT NULL,
  `state` varchar(20) NOT NULL DEFAULT 'unknown',
  `priority` int(11) NOT NULL DEFAULT 0,
  `hasDbConnection` tinyint(4) NOT NULL DEFAULT 0,
  `lastOnlineTime` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00',
  `lastMasterTime` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- moderntvcloud.TaskData definition

CREATE TABLE IF NOT EXISTS `TaskData` (
  `taskId` varchar(255) NOT NULL,
  `privateData` varchar(255) NOT NULL,
  PRIMARY KEY (`taskId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- moderntvcloud.OriginNodes definition

CREATE TABLE IF NOT EXISTS `OriginNodes` (
  `id` varchar(100) NOT NULL,
  `host` varchar(100) NOT NULL,
  `enabled` tinyint(4) NOT NULL DEFAULT 0,
  `capabilities` varchar(255) DEFAULT NULL,
  `state` varchar(20) NOT NULL DEFAULT 'unknown',
  `resourceAvailable` varchar(700) NOT NULL DEFAULT '{}',
  `resourceFree` varchar(700) NOT NULL DEFAULT '{}',
  `resourceTotal` varchar(700) NOT NULL DEFAULT '{}',
  `lastOnlineTime` timestamp NULL DEFAULT NULL,
  `comment` varchar(1000) NOT NULL DEFAULT '',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- moderntvcloud.Templates definition

CREATE TABLE IF NOT EXISTS `Templates` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `account` varchar(15) NOT NULL,
  `type` enum('picture','video') DEFAULT NULL,
  `externalId` varchar(150) DEFAULT NULL,
  `comment` varchar(1000) NOT NULL DEFAULT '',
  PRIMARY KEY (`id`),
  KEY `fk_table1_Accounts1_idx` (`account`),
  CONSTRAINT `Templates_ibfk_1` FOREIGN KEY (`account`) REFERENCES `Accounts` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- moderntvcloud.Resources definition

CREATE TABLE IF NOT EXISTS `Resources` (
  `id` varchar(40) NOT NULL,
  `account` varchar(15) NOT NULL,
  `code` varchar(50) NOT NULL,
  `type` enum('picture','stream','vod') DEFAULT NULL,
  `enabled` tinyint(4) NOT NULL DEFAULT 1,
  `removed` timestamp NULL DEFAULT NULL,
  `template` int(11) DEFAULT NULL,
  `externalId` varchar(150) DEFAULT NULL,
  `tag` varchar(100) NOT NULL DEFAULT '',
  `created` timestamp NOT NULL DEFAULT current_timestamp(),
  `description` varchar(255) NOT NULL DEFAULT '',
  `requirements` varchar(255) NOT NULL DEFAULT '',
  `priority` int(11) NOT NULL DEFAULT 0,
  `data` mediumtext NOT NULL,
  `state` varchar(10) NOT NULL DEFAULT 'plan',
  `comment` varchar(1000) NOT NULL DEFAULT '',
  PRIMARY KEY (`id`),
  UNIQUE KEY `I_Resource_account_externalId` (`id`,`account`),
  KEY `fk_Resources_Accounts1_idx` (`account`),
  KEY `fk_Resources_MediaConfigurations1_idx` (`template`),
  CONSTRAINT `Resources_ibfk_1` FOREIGN KEY (`template`) REFERENCES `Templates` (`id`),
  CONSTRAINT `fk_Resources_Accounts1` FOREIGN KEY (`account`) REFERENCES `Accounts` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- moderntvcloud.Tasks definition

CREATE TABLE IF NOT EXISTS `Tasks` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `taskId` varchar(255) NOT NULL,
  `alternative` varchar(30) DEFAULT NULL,
  `resource` varchar(40) NOT NULL,
  `resourceCode` varchar(50) NOT NULL,
  `currentRevision` int(11) NOT NULL DEFAULT 0,
  `class` varchar(50) DEFAULT NULL,
  `action` varchar(20) NOT NULL DEFAULT 'pause',
  `priority` int(11) DEFAULT NULL,
  `order` int(11) NOT NULL DEFAULT 0,
  `level` int(11) NOT NULL DEFAULT 0,
  `parameters` mediumtext DEFAULT NULL,
  `creationTime` datetime DEFAULT NULL,
  `requirements` varchar(255) DEFAULT NULL,
  `requiredResources` varchar(255) DEFAULT NULL,
  `input` varchar(50) NOT NULL,
  `outputs` varchar(300) DEFAULT '',
  `comment` varchar(255) NOT NULL DEFAULT '',
  `finite` tinyint(4) NOT NULL DEFAULT 0,
  `dependsOn` varchar(255) DEFAULT NULL,
  `after` varchar(255) DEFAULT NULL,
  `requiresFinishedDeps` tinyint(1) NOT NULL DEFAULT 0,
  `privateData` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `index3` (`taskId`,`alternative`),
  KEY `fk_Tasks_Resources1_idx` (`resource`),
  CONSTRAINT `fk_Tasks_Resources1` FOREIGN KEY (`resource`) REFERENCES `Resources` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- moderntvcloud.NodeTasks definition

CREATE TABLE IF NOT EXISTS `NodeTasks` (
  `task` bigint(20) NOT NULL,
  `originNode` varchar(100) NOT NULL,
  `revision` int(11) NOT NULL,
  `state` varchar(20) NOT NULL DEFAULT '',
  `status` varchar(255) NOT NULL DEFAULT '',
  `warnings` varchar(255) NOT NULL DEFAULT '',
  `lastRunningTime` timestamp NULL DEFAULT NULL,
  `lastOnlineTime` timestamp NULL DEFAULT NULL,
  `startTime` timestamp NULL DEFAULT NULL,
  `selectedResources` varchar(255) NOT NULL DEFAULT '{}',
  `resourceUsage` varchar(255) NOT NULL,
  PRIMARY KEY (`task`,`originNode`,`revision`),
  KEY `fk_NodeTasks_Tasks1_idx` (`task`),
  KEY `fk_NodeTasks_OriginNodes1_idx` (`originNode`),
  CONSTRAINT `NodeTasks_ibfk_1` FOREIGN KEY (`task`) REFERENCES `Tasks` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- clean
SET FOREIGN_KEY_CHECKS = 0;
TRUNCATE `NodeTasks`;
TRUNCATE `Tasks`;
TRUNCATE `Resources`;
TRUNCATE `Templates`;
TRUNCATE `OriginNodes`;
TRUNCATE `TaskData`;
TRUNCATE `Controllers`;
TRUNCATE `Accounts`;
SET FOREIGN_KEY_CHECKS = 1;

-- initialize with data
INSERT INTO Accounts (id, name) VALUES('testing', 'Testing');

INSERT INTO Templates (id, name, account, `type`, externalId, comment) VALUES(1, 'Channel', 'testing', 'video', NULL, 'testing');

INSERT INTO Resources (id, account, code, `type`, enabled, removed, template, externalId, tag, created, description, requirements, priority, `data`, state, comment)
VALUES('test_xy', 'testing', 'test', 'stream', 1, NULL, 1, NULL, '', '2023-06-23 07:00:26.000', '', '', 0, '{}', 'plan', '');

INSERT INTO Tasks (id, taskId, alternative, resource, resourceCode, currentRevision, class, `action`, priority, `order`, `level`, parameters, creationTime, requirements, requiredResources, `input`, outputs, comment, finite, dependsOn, `after`, requiresFinishedDeps, privateData)
VALUES(123, 'task:123', '', 'test_xy', 'test', 0, 'ffmpeg', 'run', 10, 0, 0, '{}', NULL, '{}', '{"CPU":0}', '', '20', '', 0, NULL, NULL, 0, NULL);

