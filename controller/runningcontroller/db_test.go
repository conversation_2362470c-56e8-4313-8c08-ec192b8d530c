package runningcontroller

import (
	"testing"

	"git.moderntv.eu/mcloud/system/common"
	"git.moderntv.eu/mcloud/system/controller/db"
	"git.moderntv.eu/mcloud/system/pkg/testdb"
)

func TestDBConnection(t *testing.T) {
	connURL := testdb.InitializeDB("controller", "testdata/db-structure.sql")
	// defer testdb.Close()

	db := db.NewDB(connURL, testdb.DbLogger{})
	db.Connect()
	defer db.Disconnect()
	db.WaitForConnection()

	rows, err := db.SelectNodes()
	if err != nil {
		t.Fatal(err)
	}
	defer rows.Close()

	for rows.Next() {
		var capabilities *string
		var dbNode common.DbNode
		err = rows.Scan(&dbNode.Id, &capabilities, &dbNode.State, &dbNode.Enabled)
		if err != nil {
			t.<PERSON><PERSON>(err)
		}
	}

}
