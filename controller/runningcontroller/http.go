package runningcontroller

import (
	"encoding/json"
	"fmt"
	"net/http"
	"sort"
	"strconv"
	"time"

	"git.moderntv.eu/mcloud/system/controller"
	"git.moderntv.eu/mcloud/system/controller/task"
)

func (self *RunningController) ManagementHandler(rwr http.ResponseWriter, req *http.Request) {
	path := req.URL.Path
	query := req.URL.Query()

	locks := controller.NewLocks()

	switch path {
	case "/":
		fmt.Fprintf(rwr, "<html><head></head><body>\n")
		//fmt.Fprintf(rwr, "version: %s<br /><br />\n", VERSION) //TODO
		fmt.Fprintf(rwr, "<a href=\"/tasks\">tasks</a><br />\n")
		fmt.Fprintf(rwr, "<a href=\"/nodes\">nodes</a><br />\n")
		fmt.Fprintf(rwr, "</body></html>")
	case "/status":
		encoder := json.NewEncoder(rwr)
		status := self.getStatus()
		_ = encoder.Encode(status)
	case "/tasks":
		var tasks = make([]*task.Task, 0)
		for task := range self.cloud.TaskIterator(func(task *task.Task) bool { return true }, locks) {
			tasks = append(tasks, task)
		}
		sort.Slice(tasks, func(i, j int) bool {
			if tasks[i].DbTask().Id == tasks[j].DbTask().Id {
				return tasks[i].DbTask().DbId > tasks[j].DbTask().DbId
			}
			return tasks[i].DbTask().Id > tasks[j].DbTask().Id
		})

		fmt.Fprintf(rwr, "<html><head></head><body>\n")
		for _, task := range tasks {
			dbTask := task.DbTask()
			fmt.Fprintf(rwr, "<a href=\"/planning?id=%s&dbid=%d\">%s</a><br />\n", dbTask.Id, dbTask.DbId, dbTask)
		}
		fmt.Fprintf(rwr, "</body></html>")
	case "/nodes":
		var nodes = self.cloud.GetNodes(locks)

		fmt.Fprintf(rwr, "<html><head></head><body>\n")
		for _, node := range nodes {
			fmt.Fprintf(rwr, "<a href=\"/node?id=%s\">%s</a><br />\n", node.Id, node.Host)
		}
		fmt.Fprintf(rwr, "</body></html>")
	case "/metrics":
		self.metricsHandler.ServeHTTP(rwr, req)
		return

	case "/node":
		var ID = query.Get("id")
		if ID == "" {
			http.Error(rwr, "missing node ID", http.StatusBadRequest)
			return
		}
		var node = self.cloud.GetNode(ID, locks)
		if node == nil {
			http.Error(rwr, "no such node", http.StatusNotFound)
			return
		}
		var dbNode = node.DbNode

		fmt.Fprintf(rwr, "Node '%s' at '%s' in '%s':\n", dbNode.Id, dbNode.Host, dbNode.Group)
		if dbNode.Enabled {
			fmt.Fprintf(rwr, "- enabled\n")
		} else {
			fmt.Fprintf(rwr, "- DISABLED\n")
		}
		if dbNode.LastOnline != nil && time.Since(*dbNode.LastOnline) < 10*time.Second {
			fmt.Fprintf(rwr, "- is currently '%s'\n", dbNode.State)
		} else {
			ago := ""
			if dbNode.LastOnline != nil {
				ago = fmt.Sprintf(" %s ago", time.Since(*dbNode.LastOnline).String())
			}
			fmt.Fprintf(rwr, "- was '%s'%s\n", dbNode.State, ago)
		}
		if dbNode.Capabilities != nil {
			fmt.Fprintf(rwr, "- capabilities:")
			var caps []string
			for cap := range *dbNode.Capabilities {
				caps = append(caps, cap)
			}
			sort.Slice(caps, func(i, j int) bool { return caps[i] < caps[j] })
			for _, cap := range caps {
				fmt.Fprintf(rwr, " %s", cap)
			}
			fmt.Fprintf(rwr, "\n")
		}
		fmt.Fprintf(rwr, "\n")

		fmt.Fprintf(rwr, "[Resources]\n")
		var resources []string
		for res := range dbNode.TotalResources {
			resources = append(resources, res)
		}
		sort.Slice(resources, func(i, j int) bool { return resources[i] < resources[j] })

		for _, res := range resources {
			total := dbNode.TotalResources[res]
			free := dbNode.FreeResources[res]
			avail, reservations := node.GetAvailableResource(res, locks)
			if len(free) != len(total) || len(avail) != len(total) {
				http.Error(rwr, "bad node data", http.StatusInternalServerError)
				return
			}
			fmt.Fprintf(rwr, "%s:", res)
			for i := 0; i < len(total); i++ {
				fmt.Fprintf(rwr, " %.0f/%.0f/%.0f", avail[i], free[i], total[i])
			}
			fmt.Fprintf(rwr, "\n")
			var resIds []string
			for id := range reservations {
				resIds = append(resIds, id)
			}
			sort.Slice(resIds, func(i, j int) bool { return resIds[i] < resIds[j] })
			for _, id := range resIds {
				reserved := reservations[id]
				fmt.Fprintf(rwr, "  %s:", id)
				for i := 0; i < len(reserved); i++ {
					fmt.Fprintf(rwr, " %.0f", reserved[i])
				}
				fmt.Fprintf(rwr, "\n")
			}
		}
	case "/planning":
		taskDbId_ := query.Get("dbid")
		taskId := query.Get("id")
		if taskDbId_ == "" || taskId == "" {
			http.Error(rwr, "missing dbid or id parameter", http.StatusBadRequest)
			return
		}
		taskDbId, err := strconv.ParseInt(taskDbId_, 10, 64)
		if err != nil {
			http.Error(rwr, err.Error(), http.StatusBadRequest)
			return
		}
		task := self.cloud.GetTask(taskId, int(taskDbId), locks)
		if task == nil {
			http.Error(rwr, "no such task", http.StatusNotFound)
			return
		}

		fmt.Fprintf(rwr, "[Current state]\n")
		if running, _, _ := task.GetRunning(0, locks); running {
			fmt.Fprintf(rwr, "this task is running\n")
		} else if running, _, _ := task.GetRunning(controller.CountAnyRevision, locks); running {
			fmt.Fprintf(rwr, "different revision of this task is running\n")
		} else if self.cloud.IsTaskIdRunning(task.DbTask().Id, controller.CountAnyRevision, locks) {
			fmt.Fprintf(rwr, "different alternative of this task id is running\n")
		} else if self.cloud.IsTaskIdRunning(task.DbTask().Id, controller.CountAnyRevision|controller.CountAnyLevel, locks) {
			fmt.Fprintf(rwr, "higher level alternative of this task id is running\n")
		} else if self.cloud.IsTaskIdRunning(task.DbTask().Id, controller.CountAnyRevision|controller.CountAnyState, locks) {
			fmt.Fprintf(rwr, "task id is running but not in the 'running' state\n")
		} else {
			fmt.Fprintf(rwr, "no task with this task id running\n")
		}
		fmt.Fprintf(rwr, "\n")

		fmt.Fprintf(rwr, "[Required action]\n")
		_, err = self.cloud.HandleTask(task, rwr, locks)
		controller.AssertLock(locks.Tasks, controller.LockUnlocked)
		controller.AssertLock(locks.Task, controller.LockUnlocked)
		if err != nil {
			fmt.Fprintf(rwr, "error: %s\n", err)
		}
		index := int64(self.cloud.TaskListIndex(task, locks))
		position := "not present"
		if index != -1 {
			position = strconv.FormatInt(index, 10)
		}
		fmt.Fprintf(rwr, "planning queue position: %s\n", position)
		fmt.Fprintf(rwr, "\n")

		fmt.Fprintf(rwr, "[Node selection]\n")
		node, selectedResources, err := self.cloud.SelectNode(task, rwr, locks)
		controller.AssertLock(locks.Tasks, controller.LockUnlocked)
		controller.AssertLock(locks.Task, controller.LockUnlocked)
		if err == nil {
			var resources string
			for res, index := range selectedResources {
				if resources != "" {
					resources += " "
				}
				resources += fmt.Sprintf("%s=%d", res, index)
			}
			fmt.Fprintf(rwr, "\nnode '%s' would be selected with resource indexes: %s\n", node.Id, resources)
		} else {
			fmt.Fprintf(rwr, "\nERROR: %s\n", err)
		}
	default:
		http.Error(rwr, "not found", http.StatusNotFound)
		return
	}
}

func (self *RunningController) getStatus() Status {
	state := "WARN"
	locks := controller.NewLocks()
	controllerMessage := "in transition"
	if self.State(StateIdle, locks) {
		state = "OK"
		controllerMessage = "in idle"
	} else if self.State(StateMaster, locks) {
		state = "OK"
		controllerMessage = "is master"
	} else if self.State(StateError, locks) {
		state = "ERROR"
		controllerMessage = "reporting error"
	}

	return Status{
		Status:   state,
		Version:  self.version,
		Hostname: self.dbController.Host,
		Components: struct {
			Controller ControllerStatus "json:\"controller\""
		}{
			Controller: ControllerStatus{
				Status:  state,
				Message: controllerMessage,
			},
		},
	}
}
