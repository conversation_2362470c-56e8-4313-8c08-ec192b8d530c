package nodelist

import (
	"sort"
	"strings"

	"git.moderntv.eu/mcloud/system/common"
	"git.moderntv.eu/mcloud/system/controller"
	"git.moderntv.eu/mcloud/system/controller/node"
)

type ListNode struct {
	index int
	*node.Node
}

type NodeList struct {
	res   string
	nodes []*ListNode
	m     map[string]*ListNode
}

func NewNodeList(res string) *NodeList {
	return &NodeList{
		res:   res,
		nodes: make([]*ListNode, 0, 16),
		m:     make(map[string]*ListNode),
	}
}

func (nodeList *NodeList) Copy() []*node.Node {
	nodes := make([]*node.Node, 0, len(nodeList.nodes))

	for _, node := range nodeList.nodes {
		nodes = append(nodes, node.Node)
	}

	return nodes
}

func (nodeList *NodeList) Len() int {
	return len(nodeList.nodes)
}

func (nodeList *NodeList) Less(a int, b int) bool {
	nodeA := nodeList.nodes[a]
	nodeB := nodeList.nodes[b]

	_, okA := nodeA.FreeResources[nodeList.res]
	_, okB := nodeB.FreeResources[nodeList.res]

	if !okA && !okB {
		return strings.Compare(nodeA.Id, nodeB.Id) == -1
	}

	nodeARes, _ := nodeA.GetAvailableResource(nodeList.res, controller.NewLocks())
	nodeBRes, _ := nodeB.GetAvailableResource(nodeList.res, controller.NewLocks())
	return common.Max(nodeARes) > common.Max(nodeBRes)
}

func (nodeList *NodeList) Swap(i int, j int) {
	nodeList.nodes[i], nodeList.nodes[j] = nodeList.nodes[j], nodeList.nodes[i]
	nodeList.nodes[i].index = i
	nodeList.nodes[j].index = j
}

func (nodeList *NodeList) Add(node *node.Node) {
	if nodeList.m[node.Id] == nil {
		lNode := &ListNode{
			Node: node,
		}
		nodeList.nodes = append(nodeList.nodes, lNode)
		lNode.index = len(nodeList.nodes) - 1
		nodeList.m[node.Id] = lNode
	}

	sort.Sort(nodeList)
}

func (nodeList *NodeList) Fix(node *node.Node) {
	lNode := nodeList.m[node.Id]
	if lNode != nil {
		sort.Sort(nodeList) //TODO
	}
}

func (nodeList *NodeList) Remove(node *node.Node) {
	lNode := nodeList.m[node.Id]
	if lNode == nil {
		return
	}
	// remove from the list
	nodeList.nodes = append(nodeList.nodes[:lNode.index], nodeList.nodes[lNode.index+1:]...)

	// fix indexes
	for _, afterNode := range nodeList.nodes[lNode.index:] {
		afterNode.index -= 1
	}

	lNode.index = -1
	delete(nodeList.m, lNode.Id)
}
