#!/bin/bash

output_dir="$1"
specification="$2"

duration=$(echo "$specification" | jq -r '.duration')
out=$(echo "$specification" | jq -r '.outputs[0].output')
bad_child=$(echo "$specification" | jq -r '.bad_child')

echo "Starting the updater with duration $duration"

if [ "$bad_child" == "true" ]; then
	nohup /bin/bash -c "while true; do sleep 5; echo child output; done" &
fi

running_time=0
while [ "$duration" == "null" ] || [ "$running_time" -lt "$duration" ]; do
	echo "Running for $running_time seconds"
	if [ "$out" != "null" ]; then
		date --rfc-3339=seconds > "$out"
	fi

	sleep 5
	running_time=$((running_time + 5))
done
