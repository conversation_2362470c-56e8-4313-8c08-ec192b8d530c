package updater

import (
	"fmt"
	"path/filepath"
)

func Check(binDir string, outputDir string, specificationJson []byte, args []string) (code int, info string, err error) {
	/*
		var specification classes.CheckSpecificationSimple
		err = json.Unmarshal(specificationJson, &specification)
		if err != nil {
			return
		}
	*/

	fmt.Printf("Check: %s\n", outputDir)

	var outputs []string
	outputs, err = filepath.Glob(fmt.Sprintf("%s/*", outputDir))
	if err != nil {
		return
	}
	for _, o := range outputs {
		output := filepath.Base(o)
		fmt.Printf("Output: %s, base: %s\n", o, output)
	}

	return
}
