package natsmock

import (
	"reflect"
	"sync"
	"testing"

	"github.com/nats-io/nats.go"
)

type testData struct {
	A string
	B int
}

func TestClient(t *testing.T) {
	nc := Connect()
	enc, err := nats.NewEncodedConn(nc, nats.JSON_ENCODER)
	if err != nil {
		t.<PERSON>(err)
	}

	expected := &testData{
		A: "message",
		B: 123,
	}

	var wg sync.WaitGroup

	wg.Add(1)
	enc.Subscribe("try.*", func(data *testData) {
		if !reflect.DeepEqual(data, expected) {
			t.Errorf("want %+v, got %+v", expected, data)
		}
		wg.Done()
	})

	enc.Publish("try.sending", &testData{
		A: "message",
		B: 123,
	})

	wg.Wait()
}
