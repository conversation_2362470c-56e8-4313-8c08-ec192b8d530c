package natsmock

import (
	"fmt"

	"github.com/nats-io/gnatsd/server"
	natsserver "github.com/nats-io/nats-server/test"
	"github.com/nats-io/nats.go"
)

var srvr *server.Server

func Connect() *nats.Conn {
	opts := natsserver.DefaultTestOptions
	opts.Port = 12455
	if srvr == nil {
		srvr = natsserver.RunServer(&opts)
	}

	nc, err := nats.Connect(fmt.Sprintf("nats://127.0.0.1:%d", opts.Port))
	if err != nil {
		panic(err)
	}

	return nc
}

func Shutdown() {
	if srvr != nil {
		srvr.Shutdown()
		srvr = nil
	}
}
