package testdb

import (
	"database/sql"
	"fmt"
	"net/url"
	"os"

	"github.com/ory/dockertest/v3"
	"github.com/ory/dockertest/v3/docker"
	"github.com/tanimutomo/sqlfile"
)

var db *sql.DB
var dbResource *dockertest.Resource

func InitializeDB(dbName string, sqlFile string) (connURL string) {
	dbHost := os.Getenv("SYSTEM_TEST_MARIADB_HOST")
	var err error

	if dbHost != "" {
		connURL = fmt.Sprintf("root:test@(%s:%s)/%s?time_zone=%s", dbHost, "3306", dbName, url.QueryEscape("'+00:00'"))
		db, err = sql.Open("mysql", connURL)
		if err != nil {
			panic(err)
		}

		err = db.Ping()
		if err != nil {
			db = nil
			panic(err)
		}
	} else {
		pool, err := dockertest.NewPool("")
		if err != nil {
			panic(err)
		}

		name := "mariadb-golang-test"
		dbResource, ok := pool.ContainerByName(name)

		if !ok {
			dbResource, err = pool.RunWithOptions(&dockertest.RunOptions{
				Name:       name,
				Repository: "mariadb",
				Tag:        "10.5-focal",
				Env: []string{
					"MARIADB_ROOT_PASSWORD=test",
					"MARIADB_DATABASE=" + dbName,
				},
			}, func(config *docker.HostConfig) {
				config.AutoRemove = true
				config.RestartPolicy = docker.RestartPolicy{Name: "no"}
			})
			if err != nil {
				panic(err)
			}
		}

		err = pool.Retry(func() error {
			connURL = fmt.Sprintf("root:test@(localhost:%s)/%s?time_zone=%s", dbResource.GetPort("3306/tcp"), dbName, url.QueryEscape("'+00:00'"))
			db, err = sql.Open("mysql", connURL)
			if err != nil {
				return err
			}

			return db.Ping()
		})
		if err != nil {
			db = nil
			panic(err)
		}
	}

	sf := sqlfile.New()
	err = sf.File(sqlFile)
	if err != nil {
		panic(err)
	}

	_, err = sf.Exec(db)
	if err != nil {
		panic(err)
	}

	return
}

func Close() {
	if dbResource != nil {
		dbResource.Close()
		dbResource = nil
	}
}
