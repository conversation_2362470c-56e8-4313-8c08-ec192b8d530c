package flock

import (
	"errors"
	"os"
	"syscall"
	"time"
)

var (
	TimedOut = errors.New("flock syscall timed out")
)

type Flock struct {
	file *os.File
	fd   int
}

func FlockFile(path string, exclusive bool, nonblocking bool) (*Flock, error) {
	var err error
	var file *os.File
	var fd int

	file, err = os.Open(path)
	if err != nil {
		return nil, err
	}

	var flags int
	if exclusive {
		flags = syscall.LOCK_EX
	} else {
		flags = syscall.LOCK_SH
	}

	if nonblocking {
		flags |= syscall.LOCK_NB
	}

	fd = int(file.Fd())
	err = syscall.Flock(fd, flags)
	if err != nil {
		file.Close()
		return nil, err
	}
	var statCurrent syscall.Stat_t
	err = syscall.Lstat(path, &statCurrent)
	if err != nil {
		file.Close()
		return nil, err
	}
	var statLocked syscall.Stat_t
	err = syscall.Fstat(fd, &statLocked)
	if err != nil {
		file.Close()
		return nil, err
	}

	if statLocked.Ino != statCurrent.Ino {
		file.Close()
		return nil, errors.New("inode mismatch")
	}

	return &Flock{
		fd:   fd,
		file: file,
	}, nil
}

// warning: this function may create a resource leak
func FlockFileTimeout(path string, exclusive bool, timeout time.Duration) (*Flock, error) {
	timer := time.NewTimer(timeout)
	defer timer.Stop()

	var flock *Flock
	result := make(chan error, 1)

	go func() {
		var err error
		flock, err = FlockFile(path, exclusive, false)

		result <- err
	}()

	var err error
	select {
	case err = <-result:
		if err != nil {
			if flock != nil {
				_ = flock.FUnlock()
			}
			return nil, err
		}
	case <-timer.C:
		return nil, TimedOut
	}

	return flock, nil
}

func (self *Flock) FUnlock() error {
	if self.file == nil {
		return nil
	}
	defer func() {
		self.file.Close()
		self.file = nil
		self.fd = -1
	}()

	err := syscall.Flock(self.fd, syscall.LOCK_UN)
	if err != nil {
		return err
	}

	return nil
}
