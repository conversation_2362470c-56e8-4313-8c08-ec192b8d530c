#!/bin/bash

DIR="$( cd -- "$( dirname -- "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
WD="/tmp/mcloud-task"

# parse CLI options
# -d, --duration <duration>  duration of the task
# -f, --fail                 task fails to transfer to running state
# -h, --help                 display this help and exit
DURATION=""
FAIL=false
BAD_CHILD=""

print_help() {
  echo "Usage: $0 [options]"
  echo "Options:"
  echo "  -d, --duration <duration>  duration of the task"
  echo "  -f, --fail                 task fails to transfer to running state"
  echo "  -b, --bad-child            task has a bad child which won't terminate and writes to stdout"
  echo "  -h, --help                 display this help and exit"
}

while [[ "$#" -gt 0 ]]; do
  case $1 in
    -d|--duration)
      DURATION="$2"
      shift
      ;;
    -f|--fail)
      FAIL=true
      ;;
    -h|--help)
      print_help
      exit 0
      ;;
    -b|--bad-child)
      BAD_CHILD="\\\"bad_child\\\":true,"
      ;;
    *)
      echo "Unknown option: $1"
      print_help
      exit 1
      ;;
  esac
  shift
done

FINITE=false
DURATION_PARAM=""
if [ -n "$DURATION" ]; then
  FINITE=true
  DURATION_PARAM="\\\"duration\\\":$DURATION,"
fi

# rm -rf "$WD"

mkdir -p "$WD/tasks" "$WD/outputs" "$WD/snapshots"

sudo /usr/bin/dumb-init -- "$DIR/bin/task" \
  -spool-dir "$WD/tasks" \
  -class-dir "$DIR/test-classes" \
  -outputs-dir "$WD/outputs" \
  -snapshot-dir "$WD/snapshots" \
  -exec-user "$USER" \
  -exec-group "$USER" \
  -task '{
    "db_id":1,
    "id":"updater:main",
    "class":"updater",
    "resource":"updater_stream",
    "resource_code":"updater",
    "alternative":"alt1",
    "order":0,
    "level":0,
    "revision":**********,
    "action":"run",
    "finite":'$FINITE',
    "creation":null,
    "requirements":{"account_moderntv":true,"stream":true,"transcode":true},
    "required_resources":{"CPU":124,"GPU":26,"GPU_dec":10,"GPU_enc":20.97,"GPU_mem":1850,"disk_e":553.45,"mem":5050},
    "parameters":"{\"start\":{'"$DURATION_PARAM $BAD_CHILD"'\"outputs\": [{\"output\": \"%p/testcloud/updater/single\"}]}}",
    "priority":1000,
    "comment":"testing task",
    "depends_on":null,
    "after":null,
    "requiresFinishedDeps":false,
    "private_data":"********",
    "selected_resources":{"CPU":0,"disk_e":0,"mem":0},
    "start_delay":0
    }' \
  -nats-servers 'nats://localhost:4222' \
  -nats-token "$NATS_TOKEN"
