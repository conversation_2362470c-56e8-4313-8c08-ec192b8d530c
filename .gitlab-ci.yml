image: golang:1.22
default:
    tags:
        - docker

before_script:
    - apt-get update && apt-get install -y libcgroup-dev
    - git config --global url."https://gitlab-ci-token:${CI_JOB_TOKEN}@git.moderntv.eu/".insteadOf "https://git.moderntv.eu/"
stages:
    - build
    - test
    - benchmark

make-build:
    stage: build
    script: "make build"

make-test:
    stage: test
    variables:
        MARIADB_ROOT_PASSWORD: test
        MARIADB_DATABASE: controller
        SYSTEM_TEST_MARIADB_HOST: mariadb
    services:
        - name: mariadb:10.5-focal
          alias: mariadb

    script:
        - make test

make-lint:
    stage: test
    script:
        - make lint
